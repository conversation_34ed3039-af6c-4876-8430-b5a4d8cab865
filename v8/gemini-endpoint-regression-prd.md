# Gemini Endpoint Regression Fix PRD (V8)

## 背景
在完成 Meal Plan 刷新任务之后，所有需要 Google Gemini 的功能（Quick 菜谱、结构化 Meal Plan、图片识别到文本、菜谱详情）全部返回 `HTTP 404`。UI 显示 "Failed to load recipe details" 以及 Quick 历史记录里只有占位菜谱。问题在真实账号和模拟环境中均可复现。

## 当前症状
- Quick/Meal Plan 生成失败，界面展示占位菜谱 `Pantry meal_* Idea N`。
- 菜谱详情页调用 `GeminiAPIService.generateRecipeDetail` 直接报错，向用户提示 `API error: HTTP 404`。
- 图片识别流程在 Gemini 步骤失败，Processing 视图落入错误分支。

## 根因分析
- 代码将 Gemini 模型 URL 硬编码为 `models/gemini-1.5-flash-latest:generateContent`。
- Google API 端已不再提供该 alias（或当前 key 无权限），导致请求统一返回 404。
- `RecipeGenerationService` 里还单独拼接了一次相同 URL，没有复用 `GeminiAPIService` 的常量，增加了修改难度。
- 错误处理直接透出状态码，未打印响应体，也没有尝试备用模型。

## 技术方案
1. **模型配置化**
   - 在 `GeminiAPIService` 引入配置结构，支持 primary/fallback 模型（例如 `gemini-1.5-flash` + `gemini-pro`）。
   - 模型 ID 放入新的 `GeminiConfig`（可读取 `api-keys.plist` 或编译常量），并在需要时允许热更新。

2. **统一调用路径**
   - 让 `RecipeGenerationService` 通过 `GeminiAPIService` 提供的新接口发送请求，避免再次硬编码 URL。
   - 为 Quick/Meal Plan 提供 `callGeminiAPI(prompt:generationConfig:responseMIMEType:)` 之类的统一方法。

3. **改进容错和日志**
   - 404 时打印响应体，记录模型 ID，方便排查。
   - 自动尝试 fallback 模型。如果 primary 返回 404/410，则改用备用模型再试一次。
   - 将错误类别映射到用户可读提示（例如“服务升级中，请稍后重试”）。

4. **测试与验证**
   - 新增单元测试：验证 URL 由配置生成，fallback 发生时会调用第二个模型。
   - 通过本地 mock 或注入 `URLProtocol` 模拟 404，确保错误消息和回退逻辑生效。

## 影响范围
- `Services/GeminiAPIService.swift`
- `Services/RecipeGenerationService.swift`
- 依赖 `GeminiAPIService` 的调用点（Quick/Meal Plan/Processing）。
- `Utilities/APIKeys.swift`（若需要存储模型配置）。

## 风险与缓解
- **风险**：fallback 模型返回的 JSON 结构不完全一致。
  - **缓解**：在解析前做 schema 校验，必要时对不同模型的输出做兼容处理。
- **风险**：统一调用路径时引入并发/actor 冲突。
  - **缓解**：保持 `GeminiAPIService` 为 actor，对外提供 `async` API，并在 `RecipeGenerationService` 中通过该 actor 调用。

## 验收标准
1. Quick/Meal Plan/菜谱详情/图片识别在有效 API Key 下全部返回 200 并展示真实内容。
2. 当 primary 模型不可用时，fallback 自动生效，日志记录模型 ID 与错误信息。
3. 单元测试通过，覆盖配置化 URL 和 fallback 分支。
4. QA 验证至少一次手动生成（Quick + Meal Plan）和一次图片识别流程。

## 时间线与负责人
- 负责人：iOS 团队（Codex 协助诊断）
- 预计开发：1 天
- 测试与验证：0.5 天

## 开放问题
- 是否需要在设置页或 Debug 菜单暴露模型切换？
- fallback 模型费用是否高于 primary，需要产品确认预算。

