# Meal Plan Refresh Bug Analysis

## Problem Statement
When a structured meal plan is generated from the Generator flow, the app navigates to **Recipes → Plans** but continues to show the empty-state view. The newly created plan only becomes visible after the app is relaunched. This behaviour confuses users, who believe the generation failed even though the data was saved successfully.

## Observed Behaviour
1. The user configures and generates a custom meal plan.
2. `RecipeGeneratorViewModel` saves the generated `MealPlan` via `PlanStore.mergeAndSave(newPlan:policy:)` (see `Services/PlanStore.swift:90-117`).
3. The view model sets `UserDefaults.standard["recipes.selectedTab"] = "plans"` and uses `NavigationCoordinator.switchToTab(3)` to show the Plans tab.
4. `PlansHistoryView` still displays the `PlansEmptyStateView` (“Plan your week with ease”).
5. After force quitting and relaunching the app, the plan appears correctly under Plans.

## Technical Root Cause
- `PlansHistoryView` owns a `@State private var viewModel = RecipesViewModel()` (`Features/Recipes/PlansHistoryView.swift:4-14`).
- `RecipesViewModel` only loads data in its initializer by calling `reload()` once (`Features/Recipes/RecipesViewModel.swift:15-24`).
- When `PlanStore.mergeAndSave` writes the new plan, no change notification is emitted, and the existing `RecipesViewModel` instance is never told to refresh.
- Because the view model’s cached `lastMealPrep` remains `nil`, SwiftUI keeps rendering the empty state. The data only becomes visible after a restart because the view model is re-created and its initializer runs again.

In short, UI state is stale: the plan save succeeds, but the Plans tab does not observe or reload the updated storage.

## Impact
- Users perceive that meal-plan generation failed.
- Support tickets are likely to increase due to apparent data loss.
- Engagement with the structured planning feature drops because the immediate feedback loop is broken.

## Reproduction Checklist
1. Ensure pantry has items and feature flags are set to defaults.
2. Generate any meal plan (e.g., 1 day, Breakfast + Dinner).
3. Observe that the Plans tab remains empty right after generation.
4. Relaunch the app; the plan now appears.

## Proposed Fixes
1. **Refresh on Appearance**: Call `viewModel.reload()` inside `PlansHistoryView.onAppear` so the latest plan is fetched whenever the view becomes visible.
2. **Listen for Plan Updates**: Introduce a `Notification.Name.planStoreDidChange` (or similar) that `PlanStore.mergeAndSave` posts after persisting. `RecipesViewModel` or `PlansHistoryView` should subscribe and call `reload()` when the notification fires.
3. **Optional cache invalidation**: Expose a `PlanStore.observeChanges()` async sequence or use an `@Observable` store to push updates without manual notifications.

## Recommended Implementation Path
1. Add a lightweight notification emission in `PlanStore.mergeAndSave` after the `UserDefaults` write.
2. Update `RecipesViewModel` to listen for that notification in its initializer and call `reload()` on receipt.
3. Add a defensive `.onAppear { viewModel.reload() }` in `PlansHistoryView` to cover the navigation path from the Generator.
4. Write a regression UI test (or unit test around `RecipesViewModel`) ensuring that a saved plan becomes visible without restarting the app.

## Additional Considerations
- Ensure notification delivery happens on the main actor to avoid mismatched state mutations.
- If multiple tabs use the same `RecipesViewModel`, share a single observable instance via dependency injection to prevent duplicated work.
- Consider surfacing a success toast/banner once the plan save banner is displayed so users get immediate confirmation.

## Conclusion
The bug stems from stale UI state rather than a failed generation. By reloading or observing `PlanStore` changes when the Plans tab is shown, the new meal plan will appear immediately after generation, restoring user confidence in the feature.
