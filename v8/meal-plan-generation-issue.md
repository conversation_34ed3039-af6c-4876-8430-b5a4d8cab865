# Meal Plan Generation Failure Report

## 1. Summary
Custom meal-plan generation intermittently returns a completely empty `MealPlan`. The UI shows the generation spinner, switches to **Recipes → Plans**, but the user only sees the default empty state ("Plan your week with ease"). The pipeline actually terminates early because the structured generator thinks the pantry is empty and returns `MealPlan(days: [])`. `PlanStore.mergeAndSave` then short-circuits, so no data is ever persisted.

## 2. Environment
- Branch: `v8` stabilization build (June 2024 snapshot)
- Platform: iOS 17 simulator & device
- Feature flag state: defaults (Parallelization ON, Prefetch OFF for structured path)
- Pantry contents: populated (Quick mode succeeds and shows pantry-driven recipes)

## 3. Reproduction Steps
1. Launch the app, ensure pantry already contains items.
2. Navigate to **Generator → Meal Plan**.
3. Configure:
   - Days: `1`
   - Start Date: any future date (e.g., `2024-06-23`)
   - Meals: Breakfast, Lunch, Dinner (any counts 1–6)
4. Tap **Generate**.

### Actual Result
- Spinner shows, the Generator view dismisses the banner.
- App jumps to **Recipes → Plans** tab but displays the empty-state screen (no calendar, only CTA buttons).

### Expected Result
- Structured plan should appear in Plans calendar, with seven-day retention behavior intact.

## 4. Technical Diagnosis

### 4.1 Guard in `StructuredMealPlanGenerator`
Code path (`Services/StructuredMealPlanGenerator.swift:31-38`):
```swift
let pantryCount = await pantryService.pantryItems.count
guard pantryCount > 0 else { return MealPlan(days: []) }

let slots = enumerateSlots(request: request, now: now)
if slots.isEmpty { return MealPlan(days: []) }
```
If `pantryItems` is empty, we return an empty plan without surfacing any error.

### 4.2 Async Pantry Load Race
`PantryService` loads SwiftData items on a detached `Task` inside its initializer (`Services/PantryService.swift:18-46`). During app launch there is a window where `pantryItems` is still `[]` even though persistent storage has data. Quick mode does not guard against an empty pantry, so it continues and simply returns zero recipes. The structured generator, however, exits immediately because of the guard above.

### 4.3 Downstream Effects
- `PlanStore.mergeAndSave(newPlan:, policy:)` ignores empty plans (`Services/PlanStore.swift:90-93`).
- `RecipesViewModel.lastMealPrep` stays `nil`, so `PlansHistoryView` renders `PlansEmptyStateView` (`Features/Recipes/PlansHistoryView.swift:6-16`).
- There is no toast/banner because the run technically succeeded.

### 4.4 Signals & Telemetry Gaps
- No log identifies the guard path, so the failure is silent.
- We have no unit test covering "pantry in flight" scenario for the structured generator.

## 5. Proposed Remediation

### 5.1 Surface Explicit Errors
Replace the silent guard with an error case that propagates to the view model:
```swift
// Services/StructuredMealPlanGenerator.swift
let pantryCount = await pantryService.pantryItems.count
if pantryCount == 0 {
    throw RecipeGenerationError.noPantryItems
}
```
The view model already handles `.pantryEmpty` and `.serviceError`, so we can map `RecipeGenerationError.noPantryItems` to `DisplayError.pantryEmpty`.

### 5.2 Wait for Pantry Readiness
Introduce a readiness API on `PantryService` (or reuse `PantryStateProvider`):
```swift
// Services/PantryService.swift
func waitUntilLoaded() async {
    await storageService.waitUntilReady()
    if pantryItems.isEmpty {
        await loadPantryItems()
    }
}
```
Call it before generation:
```swift
// RecipeGeneratorViewModel.performGeneration
await PantryService.shared.waitUntilLoaded()
```
This ensures the generator sees the actual pantry contents.

### 5.3 Guard When Slots Are Empty
Even with a ready pantry, slot enumeration can still produce zero results (cutoffs, filters). Detect this and raise a user-visible error:
```swift
let slots = enumerateSlots(request: request, now: now)
guard slots.isNotEmpty else {
    throw MealPlanGenerationError.noEligibleSlots
}
```
Map `noEligibleSlots` to a friendly message (“Selected meals already passed today’s cutoff”).

### 5.4 Add Logging & Telemetry
Add `Logger` calls before every early return, and record counters for:
- `structured_pantries_empty`
- `structured_slots_empty`

This makes it obvious in Console / telemetry dashboards when the guard fires.

### 5.5 Testing
- Unit-test a simulated pantry that finishes loading after a short delay to ensure the generator waits correctly.
- Integration test: start with empty pantry, add items, then generate to verify the new error surfaces instead of a silent void.

## 6. Code Change Outline

```diff
--- a/Services/StructuredMealPlanGenerator.swift
+++ b/Services/StructuredMealPlanGenerator.swift
@@
-        let pantryCount = await pantryService.pantryItems.count
-        guard !request.selectedMeals.isEmpty else { return MealPlan(days: []) }
-        guard pantryCount > 0 else { return MealPlan(days: []) }
+        let pantryCount = await pantryService.pantryItems.count
+        guard !request.selectedMeals.isEmpty else { return MealPlan(days: []) }
+        if pantryCount == 0 {
+            logger.error("Structured generation aborted: pantry empty")
+            throw RecipeGenerationError.noPantryItems
+        }
@@
-        if slots.isEmpty { return MealPlan(days: []) }
+        guard slots.isEmpty == false else {
+            logger.error("Structured generation aborted: no eligible slots for request")
+            throw MealPlanGenerationError.noEligibleSlots
+        }
```

```diff
--- a/Features/RecipeGenerator/RecipeGeneratorViewModel.swift
+++ b/Features/RecipeGenerator/RecipeGeneratorViewModel.swift
@@
-                        let plan = try await withTimeout(45) {
-                            try await planTask.value
-                        }
+                        let plan = try await withTimeout(45) {
+                            try await planTask.value
+                        }
@@
-            } catch is TimeoutError {
+            } catch is TimeoutError {
                 ...
+            } catch RecipeGenerationError.noPantryItems {
+                await MainActor.run {
+                    viewState = .failed(.pantryEmpty)
+                }
+            } catch MealPlanGenerationError.noEligibleSlots {
+                await MainActor.run {
+                    viewState = .failed(.configurationError("no_slots"))
+                }
             } catch {
                 ...
             }
```

```diff
--- a/Services/PantryService.swift
+++ b/Services/PantryService.swift
@@
     init() {
-        Task {
-            await storageService.waitUntilReady()
-            await resetPantryIfFirstLaunch()
-            await loadPantryItems()
-        }
+        Task {
+            await preparePantry()
+        }
     }
+
+    func preparePantry() async {
+        await storageService.waitUntilReady()
+        await resetPantryIfFirstLaunch()
+        await loadPantryItems()
+    }
```

Call `await PantryService.shared.preparePantry()` ahead of structured generation to avoid the race.

## 7. Root-Cause Factors
- **Asynchronous init**: relying on a detached task inside `init` without exposing readiness.
- **Silent failure**: early return hides the actual problem and makes the UI appear broken.
- **Lack of end-to-end tests**: no coverage ensuring structured plans persist when pantry data is present.

## 8. Next Steps
1. Implement the code changes above and add unit/integration tests.
2. Backfill telemetry dashboards with the new counters to monitor structured generation health.
3. Review other services for similar async-initialization guards to prevent future regressions.

Once these adjustments land, generating meals for future dates (e.g., 23rd breakfast/lunch/dinner) will consistently populate the Plans calendar instead of leaving users on the empty screen.
