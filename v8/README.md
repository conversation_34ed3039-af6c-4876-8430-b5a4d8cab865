# V8 Meal Plan Execution PRD

## Document Control
- Owner: Meal Plan Squad (Generator + Recipes pods)
- Reviewers: Core iOS Platform, Product Analytics, Culinary Ops
- Version: 1.0
- Last Updated: 2024-07-08
- Status: Approved for implementation (strict code control)

## Background
Structured meal-plan generation (V8 initiative) regressed in two critical ways: recently generated plans remain invisible until the app relaunches, and the structured generator can still emit empty plans when pantry data races or slot enumeration fails. These issues undermine user trust, increase support load, and negate the value of the parallelized generation work shipped earlier in V8.

## Problem Statements
1. **Stale Plans tab**: After `RecipeGeneratorViewModel` saves a plan, `PlansHistoryView` renders the empty state because its `RecipesViewModel` instance never reloads. Users must force-quit the app to see their plan.
2. **Silent empty-plan saves**: The structured generator can return `MealPlan(days: [])` when pantry data is loading or eligible slots are filtered out. The pipeline treats this as success, so no feedback reaches the user.

## Goals
- Ensure every successful generation surfaces in **Recipes → Plans** without relaunching.
- Deliver a crash-free, responsive structured generation flow that honors all configured constraints.
- Preserve Quick mode behaviour, API contracts, and calendar UI while enforcing strict concurrency and resource limits.

## Non-Goals
- No redesign of Quick mode, Manage UI, or external service contracts.
- No change to feature flag defaults beyond those specified below.
- No relaxation of existing validation bounds (dish counts, durations, etc.).

## Scope
### In Scope
- `RecipeGeneratorViewModel`, `StructuredMealPlanGenerator`, `RecipeServiceAdapter`, `PlanStore`, `RecipesViewModel`, `PlansHistoryView`.
- Notification/listener plumbing required to refresh the Plans tab.
- Test coverage for pantry-ready, slot-empty, fingerprint, timeout, and visibility scenarios.
- Telemetry and logging for early exits and save flows.

### Out of Scope
- Backend API modifications, new server endpoints, or changes to pantry persistence schema.
- UI rebrand outside the Apple HIG-compliant adjustments already planned.

## Success Metrics
- `ai_calls_total` ≤ 4 in the 5-day Lunch + Dinner performance scenario.
- Cancel responsiveness < 500 ms under sustained load.
- `fingerprint_match` guard prevents stale navigation 100% of the time (no false positives recorded in telemetry review).
- Crash rate 0% across validation scenarios; Plans tab shows saved plans immediately post-generation.

## Functional Requirements
### FR-1 Structured Generation (MVP Baseline)
- **FR-1.1** Start-date picker defaults to `today`, range `today...today+7`, threaded through `RecipeRequestBuilder`.
- **FR-1.2** Invoke append-only `PlanStore.mergeAndSave`, return `OverlapSaveSummary` to the UI.
- **FR-1.3** Add `prefetchDetails: Bool` flag to `RecipeServiceAdapter.generate`; structured flow passes `false`, Quick mode remains unchanged.
- **FR-1.4** Enforce per-meal isolation with `do/catch`, 12-dish cap, cancellation checks, and structured logging on failures.
- **FR-1.5** Provide reusable `withTimeout` helper (45 s) throwing `TimeoutError` handled in the view model.
- **FR-1.6** Compute navigation fingerprint (days, meals, dish counts, cooking times, cuisines, additional request, equipment, servings, restrictions, start date) and suppress redundant navigations when unchanged.
- **FR-1.7** Surface non-blocking post-save banner summarising additions by day × meal.
- **FR-1.8** Normalize `Manage` indexing via `yyyy-MM-dd` keys so newly generated slots appear immediately.
- **FR-1.9** Include every user-selected constraint in prompt construction for both Quick and Custom modes.
- **FR-1.10** Back requirements with unit tests covering fingerprint guard, prefetch flag behaviour, error isolation, date indexing, and timeout boundaries.

### FR-2 Plan Visibility & Refresh
- **FR-2.1** `PlanStore.mergeAndSave` posts `Notification.Name.planStoreDidChange` (main actor) after persisting.
- **FR-2.2** `RecipesViewModel` subscribes to the notification, calling `reload()` on receipt; subscription lifecycle avoids retain cycles and runs on `@MainActor`.
- **FR-2.3** `PlansHistoryView` triggers `viewModel.reload()` inside `.onAppear` to guarantee fresh data when navigated from the generator.
- **FR-2.4** Race-proof the save pipeline so the Plans tab updates even under heavy parallel generation.
- **FR-2.5** Add regression test ensuring a generated plan appears without restarting the app.

### FR-3 Pantry & Slot Validation
- **FR-3.1** Throw `RecipeGenerationError.noPantryItems` if pantry remains empty after readiness wait; map to `DisplayError.pantryEmpty`.
- **FR-3.2** Provide async readiness API (or reuse existing provider) ensuring pantry data loads before generation.
- **FR-3.3** Throw `MealPlanGenerationError.noEligibleSlots` when slot enumeration yields zero results; surface friendly cutoff messaging.
- **FR-3.4** Log structured counters: `structured_pantries_empty`, `structured_slots_empty`.
- **FR-3.5** Unit-test delayed pantry load and slot-empty branches.

### FR-4 Performance & Concurrency
- **FR-4.1** Disable detail prefetching permanently for structured generation (retain Quick defaults); cover with tests.
- **FR-4.2** Parallelize per-meal generation via bounded `TaskGroup` (concurrency cap 2–3); maintain per-meal error isolation.
- **FR-4.3** Keep `RecipeGeneratorViewModel` on `@MainActor` only for UI-facing state transitions; avoid awaiting long operations on the main actor.
- **FR-4.4** Offload `PlanStore` merge/encode work off the main actor, finalize `UserDefaults` writes via `MainActor.run` for atomicity.
- **FR-4.5** Measure and validate performance scenario (5-day Lunch + Dinner) with integration tests (AI calls, wall-clock time, cancel latency).

### FR-5 Observability & UX
- **FR-5.1** Maintain non-blocking banners/toasts with accessibility support; add success confirmation after saves.
- **FR-5.2** Ensure new strings land in `Localizable.strings` following existing key patterns.
- **FR-5.3** Align all UI updates with iOS HIG; no bespoke UI patterns.

## Non-Functional Requirements
- Language: Swift 5.9 with strict async/await and actor isolation.
- Platform: iOS 17 (SwiftUI UI layer) using XcodeGen (`project.yml`).
- Resource caps: dish count `1...12`, cooking time `5...120` minutes, enforced in both validation and prompt construction.
- Slot enumeration: honour `MealCutoffManager`, skip past-cutoff meals, maintain configuration fingerprints.
- Data retention: append-only merges with four-week window, preserve favourites and existing slots.
- Quick mode parity: no behavioural change or regression in Quick flow.

## Implementation Guidance
- Keep heavy work off `@MainActor`; use `TaskGroup` with `Task.checkCancellation()` in loops.
- Log and swallow per-meal errors, delivering partial plans instead of failing the entire generation.
- Maintain module layout (`Features/`, `Services/`, `Utils/`); keep comments concise.
- Adhere to Apple HIG and accessibility standards for banners and toasts.

## Milestones & Timeline
### Phase 1 – MVP Hardening (Days 1–3)
- Deliver FR-1 requirements end-to-end with corresponding unit tests.
- Ensure pantry readiness and slot validation (FR-3) land in this phase to unblock visibility work.

### Phase 2 – Plan Visibility Fix (Days 3–4)
- Implement FR-2 notification plumbing, view model refresh, and regression tests.
- Verify Plans tab updates immediately after generation via manual QA script.

### Phase 3 – Performance & Observability (Days 4–6)
- Complete FR-4 and FR-5 items, including integration tests and telemetry instrumentation.
- Validate cancel responsiveness and AI call reduction against targets.

### Stretch (Optional UX Enhancements)
- Highlights TTL, deduplication, Manage filtering refinements if bandwidth allows after core milestones.

## Acceptance Criteria & Validation
- All FR items satisfied with automated test coverage per section.
- Mandatory validation scenarios executed:
  - Complex configuration: 3 days, Breakfast 2 + Lunch 3 + Dinner 4.
  - Configuration change detection: regenerate after modification.
  - Overlap append: generate days 1–7 then 5–9.
  - Performance: 5-day Lunch + Dinner run hits AI call and latency targets.
  - Manage integration: generated recipes visible, delete/favorite/share functional.
  - Start-date and same-day cutoff handling validated near cutoff times.
- Telemetry dashboards show new counters and success metrics.

## Delivery Checklist
- [ ] Feature flags reviewed; parallelization remains ON, structured prefetch OFF by default.
- [ ] Notifications posted on main actor; observers cleaned up to prevent leaks.
- [ ] All new strings localized and accessibility verified.
- [ ] Unit and integration tests updated/passing in CI.
- [ ] Release notes drafted for QA handoff.

## Next Actions (Post-PRD Execution Guidance)
1. Align Generator + Recipes pods on implementation owners and sequencing; lock sprint scope.
2. Implement FR-2 visibility fixes first to unblock QA confirmation of the primary user-facing bug.
3. Land pantry readiness and slot validation improvements alongside visibility work to remove silent failure paths.
4. Execute automated + manual validation checklist; capture telemetry baselines before rollout.
5. Prepare deployment plan and communication to Support once QA sign-off is secured.

## Appendix A – Key File References
- `Features/RecipeGenerator/RecipeGeneratorView.swift`
- `Features/RecipeGenerator/RecipeGeneratorViewModel.swift`
- `Features/Recipes/PlansHistoryView.swift`
- `Features/Recipes/RecipesViewModel.swift`
- `Services/StructuredMealPlanGenerator.swift`
- `Services/RecipeServiceAdapter.swift`
- `Services/PlanStore.swift`
- `Services/PantryService.swift`
- `Utils/MealCutoffManager.swift`
- `Utilities/AsyncTimeout.swift`

