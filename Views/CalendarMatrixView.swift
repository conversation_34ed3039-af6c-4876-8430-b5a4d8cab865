import SwiftUI
import Combine

/// Static 7-day (Mon–Sun) × 3-meal (Breakfast/Lunch/Dinner) matrix.
/// Displays visual indicators and triggers selection callback for filled slots.
struct CalendarMatrixView: View {
    let weekDays: [Date]                // Monday-first 7 dates
    let slotsProvider: (Date, MealType) -> [MealSlot]
    let favoritesProvider: (String) -> Bool
    var onSelectSlot: (Date, MealType, [MealSlot]) -> Void

    init(
        weekDays: [Date],
        slotsProvider: @escaping (Date, MealType) -> [MealSlot],
        favoritesProvider: @escaping (String) -> Bool = { _ in false },
        onSelectSlot: @escaping (Date, MealType, [MealSlot]) -> Void
    ) {
        self.weekDays = weekDays
        self.slotsProvider = slotsProvider
        self.favoritesProvider = favoritesProvider
        self.onSelectSlot = onSelectSlot
    }

    private let meals: [MealType] = [.breakfast, .lunch, .dinner]
    @State private var currentDate: Date = Date()
    private let calendar = Calendar.current
    private let clock = Timer.publish(every: 60, on: .main, in: .common).autoconnect()
    private let mealTimes: [MealType: DateComponents] = [
        .breakfast: DateComponents(hour: 8),
        .lunch: DateComponents(hour: 12),
        .dinner: DateComponents(hour: 18)
    ]

    var body: some View {
        let highlightedSlot = nextHighlightedSlot(for: currentDate)

        return VStack(spacing: 12) {
            // Header row: empty corner + meal names
            HStack(spacing: 12) {
                Text("")
                    .frame(width: 64, alignment: .leading)
                ForEach(meals, id: \.self) { meal in
                    Text(meal.displayName)
                        .font(.footnote.weight(.semibold))
                        .frame(width: 64)
                        .foregroundStyle(.secondary)
                }
            }

            // Rows: Mon–Sun
            ForEach(Array(weekDays.enumerated()), id: \.offset) { _, day in
                HStack(spacing: 12) {
                    Text(shortWeekday(for: day))
                        .font(.footnote.weight(.semibold))
                        .frame(width: 64, alignment: .leading)
                        .foregroundStyle(.primary)

                    ForEach(meals, id: \.self) { meal in
                        let slots = slotsProvider(day, meal)
                        let count = slots.count
                        let hasPlan = count > 0
                        let anyFavorite = slots.contains { favoritesProvider($0.recipe.id) }
                        let isHighlighted = highlightedSlot.map { candidate in
                            calendar.isDate(candidate.date, inSameDayAs: day) && candidate.meal == meal
                        } ?? false

                        if hasPlan {
                            Button {
                                onSelectSlot(day, meal, slots)
                            } label: {
                                MealSlotIndicator(count: count, isHighlighted: isHighlighted, isFavorited: anyFavorite)
                            }
                            .buttonStyle(.plain)
                            .accessibilityLabel("\(shortWeekday(for: day)), \(meal.displayName), \(count) dishes")
                            .frame(width: 64, height: 40)
                        } else {
                            MealSlotIndicator(count: 0, isHighlighted: false, isFavorited: false)
                                .accessibilityLabel("\(shortWeekday(for: day)), \(meal.displayName), no plan")
                                .frame(width: 64, height: 40)
                        }
                    }
                }
            }
        }
        .onAppear { currentDate = Date() }
        .onReceive(clock) { currentDate = $0 }
    }

    private func shortWeekday(for date: Date) -> String {
        let f = DateFormatter()
        f.locale = Locale.current
        f.dateFormat = "EEE" // Mon, Tue, ...
        return f.string(from: date)
    }

    private func nextHighlightedSlot(for referenceDate: Date) -> (date: Date, meal: MealType)? {
        var filledSlots: [(date: Date, meal: MealType, scheduled: Date)] = []

        for day in weekDays {
            for meal in meals {
                let slots = slotsProvider(day, meal)
                guard slots.isEmpty == false else { continue }
                guard let scheduled = scheduledDate(for: day, meal: meal) else { continue }
                filledSlots.append((day, meal, scheduled))
            }
        }

        guard filledSlots.isEmpty == false else { return nil }

        filledSlots.sort { $0.scheduled < $1.scheduled }

        if let upcoming = filledSlots.first(where: { $0.scheduled >= referenceDate }) {
            return (upcoming.date, upcoming.meal)
        }

        // If all scheduled slots are in the past, keep focus on the most recent one.
        if let last = filledSlots.last {
            return (last.date, last.meal)
        }

        return nil
    }

    private func scheduledDate(for day: Date, meal: MealType) -> Date? {
        let normalizedDay = calendar.startOfDay(for: day)
        var components = calendar.dateComponents([.year, .month, .day], from: normalizedDay)
        let preferredTime = mealTimes[meal] ?? DateComponents(hour: 18)
        components.hour = preferredTime.hour
        components.minute = preferredTime.minute ?? 0
        components.second = 0
        return calendar.date(from: components)
    }
}

#Preview("CalendarMatrixView") {
    let calendar = Calendar.current
    let week = WeeklyMealPlan(referenceDate: Date(), calendar: calendar)
    return CalendarMatrixView(
        weekDays: week.days,
        slotsProvider: { _, _ in [] },
        favoritesProvider: { _ in false }
    ) { _, _, _ in }
    .padding()
}
