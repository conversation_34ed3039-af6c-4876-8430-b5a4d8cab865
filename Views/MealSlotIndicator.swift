import SwiftUI

/// Visual indicator for a meal slot in the calendar matrix.
/// Displays the number of dishes with optional highlight and favorite badge.
struct MealSlotIndicator: View {
    let count: Int
    let isHighlighted: Bool
    let isFavorited: Bool

    init(count: Int, isHighlighted: Bool = false, isFavorited: Bool = false) {
        self.count = count
        self.isHighlighted = isHighlighted
        self.isFavorited = isFavorited
    }

    private var backgroundShape: some Shape {
        RoundedRectangle(cornerRadius: 8, style: .continuous)
    }

    private var backgroundColor: Color {
        guard count > 0 else { return Color(.systemGray6) }
        return isHighlighted ? Color.accentColor.opacity(0.2) : Color(.systemGray6)
    }

    private var borderColor: Color {
        guard count > 0 else { return Color.secondary.opacity(0.25) }
        return isHighlighted ? Color.accentColor : Color.secondary.opacity(0.25)
    }

    private var borderWidth: CGFloat { isHighlighted && count > 0 ? 2 : 1 }

    var body: some View {
        ZStack(alignment: .topTrailing) {
            Text("\(count)")
                .font(.headline.weight(.semibold))
                .foregroundStyle(count > 0 ? Color.primary : Color.secondary)
                .frame(width: 40, height: 40)
                .background(backgroundShape.fill(backgroundColor))
                .overlay(backgroundShape.stroke(borderColor, lineWidth: borderWidth))

            if isFavorited && count > 0 {
                Image(systemName: "star.fill")
                    .font(.system(size: 10))
                    .foregroundStyle(.yellow)
                    .padding(4)
            }
        }
        .frame(width: 44, height: 44)
    }
}

#Preview("MealSlotIndicator") {
    VStack(spacing: 12) {
        MealSlotIndicator(count: 2)
        MealSlotIndicator(count: 0)
        MealSlotIndicator(count: 3, isHighlighted: true)
        MealSlotIndicator(count: 1, isHighlighted: true, isFavorited: true)
    }
}
