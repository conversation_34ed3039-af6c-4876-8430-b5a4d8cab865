import XCTest
@testable import IngredientScanner

final class MealPlanPerformanceTests: XCTestCase {

    func test_lunchDinnerScenario_invokesExactlyFourAICalls() async throws {
        let service = CountingRecipeService(resultsPerCall: 6)
        let pantryService = MockPantryService()
        let authService = MockAuthenticationService()
        await MainActor.run {
            pantryService.pantryItems = Self.makePantryItems()
            var preferences = UserPreferences.createDefault(for: "test-user")
            preferences.familySize = 5
            authService.userPreferences = preferences
        }

        let adapter = RecipeServiceAdapter(
            recipeService: service,
            pantryService: pantryService,
            detailPrefetcher: NoOpRecipeDetailPrefetcher()
        )
        let generator = StructuredMealPlanGenerator(
            adapter: adapter,
            pantryService: pantryService,
            authService: authService,
            cutoffManager: MealCutoffManager(),
            maxConcurrentMealTasks: 2
        )

        let startDate = Calendar.current.startOfDay(for: Date()).addingTimeInterval(24 * 60 * 60)
        let request = Self.performanceScenarioRequest(startDate: startDate)

        let plan = try await generator.generatePlan(request, now: startDate)

        XCTAssertEqual(plan.days.count, 5)
        let lunchSlots = plan.days.flatMap { $0.meals }.filter { $0.mealType == .lunch }.count
        let dinnerSlots = plan.days.flatMap { $0.meals }.filter { $0.mealType == .dinner }.count
        XCTAssertEqual(lunchSlots, 12, "Lunch slots should respect the cap")
        XCTAssertEqual(dinnerSlots, 12, "Dinner slots should respect the cap")

        let totalCalls = await service.totalCallCount()
        let lunchCallCount = await service.callCount(for: .lunch)
        let dinnerCallCount = await service.callCount(for: .dinner)
        XCTAssertEqual(totalCalls, 4, "Expected exactly four AI calls (two per meal)")
        XCTAssertEqual(lunchCallCount, 2)
        XCTAssertEqual(dinnerCallCount, 2)

        XCTContext.runActivity(named: "Lunch+Dinner Baseline Metrics") { activity in
            let payload = [
                "ai_calls_total=\(totalCalls)",
                "lunch_calls=\(lunchCallCount)",
                "dinner_calls=\(dinnerCallCount)",
                "lunch_slots=\(lunchSlots)",
                "dinner_slots=\(dinnerSlots)"
            ].joined(separator: "\n")
            let attachment = XCTAttachment(string: payload)
            attachment.name = "LunchDinnerBaseline"
            attachment.lifetime = .keepAlways
            activity.add(attachment)
        }
    }

    func test_parallelGenerationReducesWallClockTime() async throws {
        let sequentialService = CountingRecipeService(resultsPerCall: 6, delayNanoseconds: 200_000_000)
        let parallelService = CountingRecipeService(resultsPerCall: 6, delayNanoseconds: 200_000_000)

        let sequentialPantry = MockPantryService()
        let parallelPantry = MockPantryService()
        let auth = MockAuthenticationService()

        await MainActor.run {
            sequentialPantry.pantryItems = Self.makePantryItems()
            parallelPantry.pantryItems = Self.makePantryItems()
            var preferences = UserPreferences.createDefault(for: "test-user")
            preferences.familySize = 5
            auth.userPreferences = preferences
        }

        let startDate = Calendar.current.startOfDay(for: Date()).addingTimeInterval(24 * 60 * 60)
        let request = Self.performanceScenarioRequest(startDate: startDate)

        let sequentialAdapter = RecipeServiceAdapter(
            recipeService: sequentialService,
            pantryService: sequentialPantry,
            detailPrefetcher: NoOpRecipeDetailPrefetcher()
        )
        let sequentialGenerator = StructuredMealPlanGenerator(
            adapter: sequentialAdapter,
            pantryService: sequentialPantry,
            authService: auth,
            cutoffManager: MealCutoffManager(),
            maxConcurrentMealTasks: 1
        )

        let parallelAdapter = RecipeServiceAdapter(
            recipeService: parallelService,
            pantryService: parallelPantry,
            detailPrefetcher: NoOpRecipeDetailPrefetcher()
        )
        let parallelGenerator = StructuredMealPlanGenerator(
            adapter: parallelAdapter,
            pantryService: parallelPantry,
            authService: auth,
            cutoffManager: MealCutoffManager(),
            maxConcurrentMealTasks: 2
        )

        let sequentialStart = Date()
        _ = try await sequentialGenerator.generatePlan(request, now: startDate)
        let sequentialDuration = Date().timeIntervalSince(sequentialStart)

        let parallelStart = Date()
        _ = try await parallelGenerator.generatePlan(request, now: startDate)
        let parallelDuration = Date().timeIntervalSince(parallelStart)

        XCTAssertGreaterThan(sequentialDuration, parallelDuration)
        XCTAssertLessThan(parallelDuration, sequentialDuration * 0.75, "Parallel generation should be materially faster")

        XCTContext.runActivity(named: "Parallel vs Sequential Timing") { activity in
            let payload = [
                "sequential_duration_ms=\(Int(sequentialDuration * 1000))",
                "parallel_duration_ms=\(Int(parallelDuration * 1000))",
                "concurrency_cap=2"
            ].joined(separator: "\n")
            let attachment = XCTAttachment(string: payload)
            attachment.name = "TimingBaseline"
            attachment.lifetime = .keepAlways
            activity.add(attachment)
        }
    }

    func test_cancelledGenerationCompletesWithinHalfSecond() async throws {
        let slowService = CancellationAwareRecipeService(delayNanoseconds: 1_000_000_000)
        let pantryService = MockPantryService()
        let authService = MockAuthenticationService()
        await MainActor.run {
            pantryService.pantryItems = Self.makePantryItems()
            var preferences = UserPreferences.createDefault(for: "test-user")
            preferences.familySize = 5
            authService.userPreferences = preferences
        }

        let adapter = RecipeServiceAdapter(
            recipeService: slowService,
            pantryService: pantryService,
            detailPrefetcher: NoOpRecipeDetailPrefetcher()
        )
        let generator = StructuredMealPlanGenerator(
            adapter: adapter,
            pantryService: pantryService,
            authService: authService,
            cutoffManager: MealCutoffManager(),
            maxConcurrentMealTasks: 2
        )

        let startDate = Calendar.current.startOfDay(for: Date()).addingTimeInterval(24 * 60 * 60)
        let request = Self.performanceScenarioRequest(startDate: startDate)

        let start = Date()
        let task = Task {
            try await generator.generatePlan(request, now: startDate)
        }

        try await Task.sleep(nanoseconds: 100_000_000)
        task.cancel()

        do {
            _ = try await task.value
            XCTFail("Expected cancellation to propagate")
        } catch is CancellationError {
            // Expected path
        }

        let elapsed = Date().timeIntervalSince(start)
        let wasCancelled = await slowService.wasCancelled
        XCTAssertLessThan(elapsed, 0.5, "Cancellation should finish within 500ms")
        XCTAssertTrue(wasCancelled, "Underlying service should observe cancellation")

        XCTContext.runActivity(named: "Cancellation Latency Baseline") { activity in
            let payload = [
                "elapsed_ms=\(Int(elapsed * 1000))",
                "service_observed_cancel=\(wasCancelled)"
            ].joined(separator: "\n")
            let attachment = XCTAttachment(string: payload)
            attachment.name = "CancellationBaseline"
            attachment.lifetime = .keepAlways
            activity.add(attachment)
        }
    }

    private static func performanceScenarioRequest(startDate: Date) -> MealPlanGenerationRequest {
        MealPlanGenerationRequest(
            startDate: startDate,
            days: 5,
            selectedMeals: [.lunch, .dinner],
            slotConfigs: [
                .lunch: MealConfig(cookingTimeMinutes: 40, numberOfDishes: 3),
                .dinner: MealConfig(cookingTimeMinutes: 60, numberOfDishes: 4)
            ]
        )
    }

    private static func makePantryItems() -> [Ingredient] {
        [
            Ingredient(name: "Chicken", category: .proteins),
            Ingredient(name: "Rice", category: .grainsPastaLegumes),
            Ingredient(name: "Carrot", category: .vegetables)
        ]
    }
}

// MARK: - Test Doubles

actor CountingRecipeService: RecipeGenerationServiceProtocol {
    private let resultsPerCall: Int
    private let delayNanoseconds: UInt64
    private var callCounter: Int = 0
    private var callsByMeal: [MealType: Int] = [:]
    private var sequence: Int = 0

    init(resultsPerCall: Int, delayNanoseconds: UInt64 = 0) {
        self.resultsPerCall = resultsPerCall
        self.delayNanoseconds = delayNanoseconds
    }

    func generateMealIdeas(from ingredients: [String], preferences: RecipePreferences) async throws -> [RecipeIdea] {
        callCounter += 1
        if let meal = preferences.targetMealType {
            callsByMeal[meal, default: 0] += 1
        }

        if delayNanoseconds > 0 {
            try await Task.sleep(nanoseconds: delayNanoseconds)
        }

        return makeIdeas(for: preferences)
    }

    private func makeIdeas(for preferences: RecipePreferences) -> [RecipeIdea] {
        let mealName = preferences.targetMealType?.rawValue.capitalized ?? "Meal"
        return (0..<resultsPerCall).map { _ in
            sequence += 1
            let recipe = Recipe(
                recipeTitle: "\(mealName) Idea \(sequence)",
                description: "Delicious \(mealName.lowercased()) recipe",
                ingredients: ["Ingredient A", "Ingredient B"],
                instructions: ["Cook", "Serve"],
                nutrition: Recipe.NutritionInfo(calories: "400", protein: "20g", carbs: "30g", fat: "15g"),
                cookingTime: "\(preferences.cookingTimeInMinutes) minutes",
                servings: preferences.numberOfServings,
                difficulty: .easy
            )
            return RecipeIdea(recipe: recipe, status: .readyToCook, missingIngredients: [])
        }
    }

    func totalCallCount() -> Int { callCounter }

    func callCount(for meal: MealType) -> Int { callsByMeal[meal, default: 0] }
}

actor CancellationAwareRecipeService: RecipeGenerationServiceProtocol {
    private let delayNanoseconds: UInt64
    private(set) var wasCancelled = false

    init(delayNanoseconds: UInt64) {
        self.delayNanoseconds = delayNanoseconds
    }

    func generateMealIdeas(from ingredients: [String], preferences: RecipePreferences) async throws -> [RecipeIdea] {
        do {
            try await Task.sleep(nanoseconds: delayNanoseconds)
        } catch is CancellationError {
            wasCancelled = true
            throw CancellationError()
        }
        return []
    }
}

struct NoOpRecipeDetailPrefetcher: RecipeDetailPrefetching {
    func prefetchDetails(for models: [RecipeUIModel], pantryIngredients: [String], userPreferences: UserPreferences?) async {}
}
