import SwiftUI
import XCTest
@testable import IngredientScanner

@MainActor
final class RecipesViewModelTests: XCTestCase {
    override func tearDown() {
        PlanStore.shared.clearAll()
        FavoritesStore.shared.clearAll()
        super.tearDown()
    }

    func testReloadsWhenPlanStoreNotificationFires() async {
        PlanStore.shared.clearAll()
        FavoritesStore.shared.clearAll()

        let viewModel = RecipesViewModel()
        viewModel.lastMealPrep = nil

        let date = Date()
        let recipe = RecipeUIModel(
            id: "notification-check",
            title: "Notification Check",
            mealType: .dinner,
            dayIndex: 0,
            scheduledDate: date
        )
        let slot = MealSlot(slotId: UUID(), dayIndex: 0, mealType: .dinner, recipe: recipe)
        let day = DayPlan(date: date, meals: [slot])

        _ = await PlanStore.shared.mergeAndSave(newPlan: MealPlan(days: [day]))

        await Task.yield()
        await Task.yield()

        XCTAssertNotNil(viewModel.lastMealPrep)
        XCTAssertEqual(viewModel.lastMealPrep?.plan.days.first?.meals.count, 1)
    }

    func testPlansHistoryViewReloadsOnAppearWhenNotificationsDisabled() async {
        PlanStore.shared.clearAll()
        FavoritesStore.shared.clearAll()

        let viewModel = RecipesViewModel(observesPlanStoreChanges: false)
        viewModel.lastMealPrep = nil

        let date = Date()
        let recipe = RecipeUIModel(
            id: "on-appear-check",
            title: "On Appear Check",
            mealType: .lunch,
            dayIndex: 0,
            scheduledDate: date
        )
        let slot = MealSlot(slotId: UUID(), dayIndex: 0, mealType: .lunch, recipe: recipe)
        let day = DayPlan(date: date, meals: [slot])

        let view = PlansHistoryView(viewModel: viewModel)
        let host = UIHostingController(rootView: view)
        _ = host.view

        _ = await PlanStore.shared.mergeAndSave(newPlan: MealPlan(days: [day]))

        XCTAssertNil(viewModel.lastMealPrep)

        host.viewWillAppear(false)
        host.viewDidAppear(false)

        XCTAssertNotNil(viewModel.lastMealPrep)
        XCTAssertEqual(viewModel.lastMealPrep?.plan.days.first?.meals.count, 1)
    }
}

