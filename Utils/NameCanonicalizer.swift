import Foundation

/// Canonicalizes ingredient names with locale-aware, NFC-preserving rules.
/// Rules:
/// - Unicode NFC normalization (preserve accents)
/// - Replace slashes and dashes with a single space
/// - Remove other punctuation except apostrophes (' and ’), commas, and parentheses
/// - Title-case words; stopwords lowercased unless first word
/// - Preserve acronyms/abbreviations if letters are all uppercase (e.g., "BBQ", "A2")
/// - Leave parentheses and their contents unchanged
struct NameCanonicalizer {
    private let stopwords: Set<String> = ["and","or","of","the","a","an","in"]

    // NEW: Common plural patterns for singularization
    private let pluralRules: [(suffix: String, replacement: String)] = [
        ("ies", "y"),      // berries -> berry
        ("ves", "f"),      // loaves -> loaf
        ("oes", "o"),      // tomatoes -> tomato
        ("ses", "s"),      // glasses -> glass
        ("xes", "x"),      // boxes -> box
        ("ches", "ch"),    // peaches -> peach
        ("shes", "sh"),    // dishes -> dish
        ("s", "")          // grapes -> grape (fallback)
    ]

    // NEW: Irregular plurals
    private let irregulars: [String: String] = [
        "children": "child",
        "people": "person",
        "men": "man",
        "women": "woman",
        "teeth": "tooth",
        "feet": "foot",
        "mice": "mouse",
        "geese": "goose"
    ]

    func canonicalize(_ input: String, locale: Locale = .current) -> String {
        // Normalize to NFC (precomposed) and trim outer whitespace early
        let nfc = input.precomposedStringWithCanonicalMapping.trimmingCharacters(in: CharacterSet.whitespacesAndNewlines)
        guard !nfc.isEmpty else { return "" }

        // Split into segments outside vs inside parentheses so we can leave contents unchanged
        let segments = splitByParentheses(nfc)
        var parts: [String] = []

        for seg in segments {
            if seg.inParens {
                // Preserve content verbatim, but keep parentheses
                parts.append("(\(seg.text))")
            } else {
                // Apply punctuation normalization outside parentheses
                let replaced = replaceDashesAndSlashesWithSpace(seg.text)
                let sanitized = removeDisallowedPunctuation(replaced)
                let tokenized = tokenizeKeepingCommas(sanitized)
                let titled = titleCaseTokens(tokenized, locale: locale)
                let singularized = singularizeTokens(titled)  // NEW: Add singularization
                parts.append(joinTokensWithCommas(singularized))
            }
        }

        // Join and collapse whitespace
        let joined = parts.joined(separator: " ")
        return collapseWhitespace(joined).trimmingCharacters(in: CharacterSet.whitespacesAndNewlines)
    }

    // MARK: - Helpers

    private func splitByParentheses(_ s: String) -> [(text: String, inParens: Bool)] {
        var result: [(String, Bool)] = []
        var buffer = ""
        var depth = 0
        for ch in s {
            if ch == "(" {
                if depth == 0, !buffer.isEmpty {
                    result.append((buffer, false))
                    buffer.removeAll()
                }
                depth += 1
            } else if ch == ")" {
                depth = max(0, depth - 1)
                if depth == 0 {
                    // end of top-level parentheses, push current buffer as in-parens
                    result.append((buffer, true))
                    buffer.removeAll()
                    continue
                }
            }
            if depth > 0 {
                // Collect inside-parens without the surrounding ()
                if ch != "(" && ch != ")" { buffer.append(ch) }
            } else {
                buffer.append(ch)
            }
        }
        if !buffer.isEmpty { result.append((buffer, false)) }
        return result
    }

    private func replaceDashesAndSlashesWithSpace(_ s: String) -> String {
        // Common hyphen/dash characters and slash
        let dashSet: Set<Character> = ["-", "\u{2010}", "\u{2011}", "\u{2012}", "\u{2013}", "\u{2014}", "\u{2043}", "/"]
        return String(s.map { dashSet.contains($0) ? " " : $0 })
    }

    private func removeDisallowedPunctuation(_ s: String) -> String {
        // Allowed: letters, digits, space, apostrophes (' and ’), comma, parentheses (though typically removed earlier), and comma
        // Disallowed examples: & # ! @ * etc. Replace with space to avoid word merging.
        var out = String()
        out.reserveCapacity(s.count)
        for scalar in s.unicodeScalars {
            switch scalar {
            case "'".unicodeScalars.first!, "\u{2019}": // straight and curly apostrophe
                out.unicodeScalars.append(scalar)
            case ",":
                out.unicodeScalars.append(scalar)
            case "(":
                out.unicodeScalars.append(scalar)
            case ")":
                out.unicodeScalars.append(scalar)
            default:
                if CharacterSet.letters.contains(scalar) || CharacterSet.decimalDigits.contains(scalar) || scalar == " ".unicodeScalars.first! {
                    out.unicodeScalars.append(scalar)
                } else {
                    out.append(" ")
                }
            }
        }
        return out
    }

    private enum Token {
        case word(String)
        case comma
    }

    private func tokenizeKeepingCommas(_ s: String) -> [Token] {
        var tokens: [Token] = []
        var current = ""
        for ch in s {
            if ch == "," {
                if !current.isEmpty { tokens.append(.word(current)); current.removeAll() }
                tokens.append(.comma)
            } else if ch.isWhitespace {
                if !current.isEmpty { tokens.append(.word(current)); current.removeAll() }
            } else {
                current.append(ch)
            }
        }
        if !current.isEmpty { tokens.append(.word(current)) }
        return tokens
    }

    private func isUppercaseAcronym(_ word: String) -> Bool {
        // Consider it an acronym if it has letters and all letters are uppercase (digits allowed)
        let letters = word.unicodeScalars.filter { CharacterSet.letters.contains($0) }
        guard !letters.isEmpty else { return false }
        let lettersString = String(String.UnicodeScalarView(letters))
        return lettersString == lettersString.uppercased()
    }

    private func titleCaseTokens(_ tokens: [Token], locale: Locale) -> [Token] {
        var result: [Token] = []
        var isFirstWord = true
        for token in tokens {
            switch token {
            case .comma:
                result.append(.comma)
            case .word(let w):
                let processed: String
                if isUppercaseAcronym(w) {
                    processed = w
                } else {
                    let lower = w.lowercased(with: locale)
                    if stopwords.contains(lower) && !isFirstWord {
                        processed = lower
                    } else {
                        processed = capitalizeFirstLetter(lower, locale: locale)
                    }
                }
                result.append(.word(processed))
                isFirstWord = false
            }
        }
        return result
    }

    private func capitalizeFirstLetter(_ s: String, locale: Locale) -> String {
        guard let first = s.first else { return s }
        let head = String(first).uppercased(with: locale)
        let tail = String(s.dropFirst())
        return head + tail
    }

    private func joinTokensWithCommas(_ tokens: [Token]) -> String {
        var out: [String] = []
        var lastWasComma = false
        for token in tokens {
            switch token {
            case .comma:
                if !out.isEmpty { out[out.count - 1] = out.last! + "," }
                lastWasComma = true
            case .word(let w):
                if lastWasComma {
                    out.append(w) // ensure a space is added later by join or manual space
                } else {
                    out.append(w)
                }
                lastWasComma = false
            }
        }
        // Re-insert a space after commas for readability
        let joined = out.joined(separator: " ")
        // Fix pattern where comma immediately followed by space is missing
        return joined.replacingOccurrences(of: ", ", with: ", ")
    }

    private func collapseWhitespace(_ s: String) -> String {
        let components = s.split(whereSeparator: { $0.isWhitespace })
        return components.joined(separator: " ")
    }

    // MARK: - Singularization Methods (NEW for Bug #5)

    /// Singularize tokens to consolidate plural/singular forms
    private func singularizeTokens(_ tokens: [Token]) -> [Token] {
        return tokens.map { token in
            switch token {
            case .word(let w):
                return .word(singularize(w))
            case .comma:
                return .comma
            }
        }
    }

    /// Convert plural to singular form
    private func singularize(_ word: String) -> String {
        let lower = word.lowercased()

        // Skip if too short (avoid false positives like "is", "as")
        guard lower.count > 3 else { return word }

        // Check against common irregular plurals first
        if let singular = irregulars[lower] {
            return preserveCase(original: word, template: singular)
        }

        // Apply suffix rules
        for rule in pluralRules {
            if lower.hasSuffix(rule.suffix) {
                let base = String(lower.dropLast(rule.suffix.count))
                let singular = base + rule.replacement
                return preserveCase(original: word, template: singular)
            }
        }

        return word
    }

    /// Preserve original capitalization pattern
    private func preserveCase(original: String, template: String) -> String {
        if original.first?.isUppercase == true {
            return template.prefix(1).uppercased() + template.dropFirst()
        }
        return template
    }
}
