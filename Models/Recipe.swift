import Foundation

struct RecipePreferences: Sendable {
    var cookingTimeInMinutes: Int
    var numberOfServings: Int
    var dietaryRestrictions: [String] // e.g., ["Vegetarian", "Gluten-Free"]
    var allergiesAndIntolerances: [String] // e.g., ["egg", "peanut"]
    var strictExclusions: [String] // e.g., ["pork", "alcohol"]
    var customStrictExclusions: [String] // custom user-provided strings
    var respectRestrictions: Bool
    // Phase 1 additions
    var cuisines: [String] = []
    var additionalRequest: String? = nil
    var equipmentOwned: [String] = []
    var targetMealType: MealType? = nil
    var targetDishCount: Int? = nil
    // V12: Family composition
    var numberOfAdults: Int = 0
    var numberOfKids: Int = 0

    /// Initialize from UserPreferences
    init(from userPreferences: UserPreferences, cookingTime: Int) {
        self.cookingTimeInMinutes = cookingTime
        self.numberOfServings = userPreferences.familySize
        self.dietaryRestrictions = userPreferences.dietaryRestrictions.map { $0.rawValue }
        self.allergiesAndIntolerances = userPreferences.allergiesIntolerances.map { $0.rawValue }
        self.strictExclusions = userPreferences.strictExclusions.map { $0.rawValue }
        self.customStrictExclusions = userPreferences.customStrictExclusions
        self.respectRestrictions = userPreferences.respectRestrictions
        // V12: Extract family composition
        self.numberOfAdults = userPreferences.numberOfAdults
        self.numberOfKids = userPreferences.numberOfKids
        // other fields remain defaults; adapter may override
    }

    /// Manual initialization
    init(
        cookingTimeInMinutes: Int,
        numberOfServings: Int,
        dietaryRestrictions: [String],
        allergiesAndIntolerances: [String] = [],
        strictExclusions: [String] = [],
        customStrictExclusions: [String] = [],
        respectRestrictions: Bool = true,
        cuisines: [String] = [],
        additionalRequest: String? = nil,
        equipmentOwned: [String] = [],
        targetMealType: MealType? = nil,
        targetDishCount: Int? = nil,
        numberOfAdults: Int = 0,
        numberOfKids: Int = 0
    ) {
        self.cookingTimeInMinutes = cookingTimeInMinutes
        self.numberOfServings = numberOfServings
        self.dietaryRestrictions = dietaryRestrictions
        self.allergiesAndIntolerances = allergiesAndIntolerances
        self.strictExclusions = strictExclusions
        self.customStrictExclusions = customStrictExclusions
        self.respectRestrictions = respectRestrictions
        self.cuisines = cuisines
        self.additionalRequest = additionalRequest
        self.equipmentOwned = equipmentOwned
        self.targetMealType = targetMealType
        self.targetDishCount = targetDishCount
        self.numberOfAdults = numberOfAdults
        self.numberOfKids = numberOfKids
    }
}

public struct Recipe: Codable, Identifiable, Hashable, Sendable {
    public let id = UUID()
    public let recipeTitle: String
    public let description: String
    public let ingredients: [String]
    public let instructions: [String]
    public let nutrition: NutritionInfo
    public let cookingTime: String
    public let servings: Int
    public let difficulty: Difficulty

    // Computed properties for compatibility
    public var title: String {
        return recipeTitle
    }

    public var cookingTimeInMinutes: Int {
        // Extract minutes from cookingTime string (e.g., "30 minutes" -> 30)
        let components = cookingTime.components(separatedBy: CharacterSet.decimalDigits.inverted)
        return Int(components.first(where: { !$0.isEmpty }) ?? "30") ?? 30
    }

    public var numberOfServings: Int {
        return servings
    }

    // Placeholder tags for compatibility (can be derived from ingredients/description)
    public var tags: [String] {
        var derivedTags: [String] = []
        
        // Add difficulty as tag
        derivedTags.append(difficulty.rawValue)
        
        // Add cooking time category
        let minutes = cookingTimeInMinutes
        if minutes <= 30 {
            derivedTags.append("quick")
        } else if minutes <= 60 {
            derivedTags.append("moderate")
        } else {
            derivedTags.append("slow")
        }
        
        // Add serving size category
        if servings <= 2 {
            derivedTags.append("small-batch")
        } else if servings <= 4 {
            derivedTags.append("family")
        } else {
            derivedTags.append("large-batch")
        }
        
        return derivedTags
    }

    public enum Difficulty: String, Codable, CaseIterable, Sendable {
        case easy = "easy"
        case medium = "medium"
        case hard = "hard"
    }

    enum CodingKeys: String, CodingKey {
        case recipeTitle, description, ingredients, instructions, nutrition, cookingTime, servings, difficulty
    }

    public struct NutritionInfo: Codable, Hashable, Sendable {
        public let calories: String
        public let protein: String
        public let carbs: String
        public let fat: String

        public init(calories: String, protein: String, carbs: String, fat: String) {
            self.calories = calories
            self.protein = protein
            self.carbs = carbs
            self.fat = fat
        }
    }

    // Hashable conformance
    public func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }

    // Equatable conformance (using id for comparison)
    public static func == (lhs: Recipe, rhs: Recipe) -> Bool {
        return lhs.id == rhs.id
    }
}

struct RecipeIdea: Identifiable, Sendable {
    let id = UUID()
    let recipe: Recipe
    var status: RecipeStatus
    var missingIngredients: [String]
    
    enum RecipeStatus: Sendable {
        case readyToCook
        case almostThere
    }
}

struct RecipeListResponse: Codable, Sendable {
    let recipes: [Recipe]
}
