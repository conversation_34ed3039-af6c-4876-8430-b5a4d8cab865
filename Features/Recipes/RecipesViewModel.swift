import SwiftUI
import Combine


@Observable
@MainActor
final class RecipesViewModel {
    // Data sources
    private let planStore: PlanStore
    private let favoritesStore: FavoritesStore
    private let notificationCenter: NotificationCenter
    private var planStoreCancellable: AnyCancellable?

    // UI state
    var lastQuick: LastQuick?
    var lastMealPrep: LastMealPrep?
    var favoriteIds: [String] = []

    init(
        planStore: PlanStore = .shared,
        favoritesStore: FavoritesStore = .shared,
        notificationCenter: NotificationCenter = .default,
        observesPlanStoreChanges: Bool = true
    ) {
        self.planStore = planStore
        self.favoritesStore = favoritesStore
        self.notificationCenter = notificationCenter
        if observesPlanStoreChanges {
            planStoreCancellable = notificationCenter.publisher(for: .planStoreDidChange, object: nil)
                .receive(on: DispatchQueue.main)
                .sink { [weak self] _ in
                    self?.reload()
                }
        }
        reload()
    }


    func reload() {
        lastQuick = planStore.loadLastQuick()
        lastMealPrep = planStore.loadLastMealPrep()
        favoriteIds = favoritesStore.all()
    }
}
