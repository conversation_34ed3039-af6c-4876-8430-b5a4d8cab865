import SwiftUI

struct PlansHistoryView: View {
    @State private var viewModel: RecipesViewModel

    init(viewModel: RecipesViewModel = RecipesViewModel()) {
        _viewModel = State(initialValue: viewModel)
    }

    var body: some View {
        Group {
            if let plan = viewModel.lastMealPrep?.plan {
                MealPlanCalendarView(plan: plan)
                    .accessibilityLabel("Plans calendar matrix")
            } else {
                PlansEmptyStateView()
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            }
        }
        .onAppear { viewModel.reload() }
    }

    private func formattedDate(_ date: Date, index: Int) -> String {
        if index == 0 { return "Today" }
        if index == 1 { return "Tomorrow" }
        let f = DateFormatter(); f.dateStyle = .medium
        return f.string(from: date)
    }
}

#Preview("Plans History") {
    NavigationStack { PlansHistoryView() }
}
