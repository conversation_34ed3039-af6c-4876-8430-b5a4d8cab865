import SwiftUI


// Convenience init to support existing routing that passes Recipe
extension GeneratedRecipeDetailView {
    init(recipe: Recipe) {
        let ui = RecipeUIModel(
            id: recipe.id.uuidString,
            title: recipe.title,
            subtitle: recipe.description,
            estimatedTime: recipe.cookingTimeInMinutes,
            imageURL: nil,
            ingredientsFromPantry: recipe.ingredients,
            additionalIngredients: nil,
            difficulty: recipe.difficulty.rawValue,
            mealType: nil,
            dayIndex: nil,
            servings: recipe.servings,
            cuisine: nil,
            baseRecipe: recipe  // V12: Thread Recipe through
        )
        self.init(recipeUIModel: ui)
    }
}

struct GeneratedRecipeDetailView: View {
    let recipeUIModel: RecipeUIModel
    @Environment(\.dismiss) private var dismiss
    @Environment(RegenerateModeState.self) private var regenState: RegenerateModeState
    @Environment(NavigationCoordinator.self) private var coordinator: NavigationCoordinator
    @Environment(AuthenticationService.self) private var authService: AuthenticationService

    @StateObject private var cache = RecipeDetailCache.shared
    @State private var recipeDetail: RecipeDetail?
    @State private var isLoading = false
    @State private var errorMessage: String?

    // Fallback recipe for compatibility
    private var fallbackRecipe: Recipe {
        buildRecipe(from: recipeUIModel)
    }

    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                // Loading State
                if isLoading {
                    VStack(spacing: 16) {
                        ProgressView()
                            .scaleEffect(1.2)
                        Text("Generating detailed recipe...")
                            .font(.headline)
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity, minHeight: 200)
                    .padding()
                } else if let detail = recipeDetail {
                    // Recipe Detail Content
                    recipeDetailContent(detail)
                } else if let errorMessage = errorMessage {
                    // Error State
                    VStack(spacing: 16) {
                        Image(systemName: "exclamationmark.triangle")
                            .font(.largeTitle)
                            .foregroundColor(.orange)
                        Text("Failed to load recipe details")
                            .font(.headline)
                        Text(errorMessage)
                            .font(.body)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                        Button("Try Again") {
                            Task { await fetchRecipeDetail() }
                        }
                        .buttonStyle(.borderedProminent)
                    }
                    .frame(maxWidth: .infinity, minHeight: 200)
                    .padding()
                } else {
                    // Fallback Content (should not happen)
                    fallbackRecipeContent()
                }
            }
            .padding(.vertical)
        }
        .navigationTitle("Recipe")
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                HStack(spacing: 16) {
                    Button(action: {
                        // Enter Regenerate Mode and preselect by title
                        regenState.pendingPreselectTitle = recipeUIModel.title
                        regenState.beginSelection()
                        dismiss()
                    }) {
                        Text("Regenerate…")
                    }
                    FavoriteButton(recipeId: recipeUIModel.id)
                }
            }
            ToolbarItem(placement: .navigationBarLeading) {
                Button("Done") { dismiss() }
            }
        }
        .task {
            await fetchRecipeDetail()
        }
    }


    // MARK: - Helper Methods

    /// Fetch recipe detail from cache or API
    @MainActor
    private func fetchRecipeDetail() async {
        // Compose stable cache context (pantry + preferences)
        let pantryNames = ServiceContainer.shared.pantryService.pantryItems.map { $0.name }
        let prefs: RecipePreferences = {
            if let user = authService.userPreferences {
                var p = RecipePreferences(from: user, cookingTime: recipeUIModel.estimatedTime ?? 30)
                p.cuisines = recipeUIModel.cuisine.map { [$0] } ?? []
                p.equipmentOwned = user.equipmentOwned
                return p
            } else {
                return RecipePreferences(cookingTimeInMinutes: recipeUIModel.estimatedTime ?? 30, numberOfServings: recipeUIModel.servings ?? 2, dietaryRestrictions: [], allergiesAndIntolerances: [], strictExclusions: [], respectRestrictions: true)
            }
        }()

        // Check cache first (stable key)
        if let cached = cache.getCachedDetail(forTitle: recipeUIModel.title, pantryNames: pantryNames, preferences: prefs) {
            recipeDetail = cached
            return
        }

        // Fetch from API
        isLoading = true
        errorMessage = nil

        do {
            let geminiService = GeminiAPIService()
            let detail: RecipeDetail

            if let baseRecipe = recipeUIModel.baseRecipe {
                // V12: Use baseRecipe for grounded generation
                detail = try await geminiService.generateRecipeDetail(
                    baseRecipe: baseRecipe,
                    pantryContext: nil,
                    equipmentOwned: authService.userPreferences?.equipmentOwned ?? []
                )
            } else {
                // FALLBACK: Use old method for backward compatibility
                detail = try await geminiService.generateRecipeDetail(
                    title: recipeUIModel.title,
                    pantryContext: nil,
                    cuisines: recipeUIModel.cuisine.map { [$0] } ?? [],
                    equipmentOwned: authService.userPreferences?.equipmentOwned ?? []
                )
            }

            // Cache the result with stable key
            cache.cacheDetail(detail, forTitle: recipeUIModel.title, pantryNames: pantryNames, preferences: prefs)
            recipeDetail = detail

        } catch {
            errorMessage = error.localizedDescription
        }

        isLoading = false
    }

    /// Display recipe detail content
    @ViewBuilder
    private func recipeDetailContent(_ detail: RecipeDetail) -> some View {
        VStack(alignment: .leading, spacing: 20) {
            // Recipe Title and Info
            VStack(alignment: .leading, spacing: 10) {
                Text(detail.title)
                    .font(.largeTitle)
                    .fontWeight(.bold)

                // NEW: Description section
                if let description = detail.description, !description.isEmpty {
                    Text(description)
                        .font(.body)
                        .foregroundColor(.secondary)
                        .padding(.top, 4)
                }

                HStack(spacing: 16) {
                    Label("\(detail.servings) servings", systemImage: "person.2")
                    Label(detail.formattedTime, systemImage: "clock")
                    Label(detail.difficulty.capitalized, systemImage: "chart.bar")
                        .foregroundColor(difficultyColor(detail.difficulty))
                }
                .font(.subheadline)
                .foregroundColor(.secondary)
            }
            .padding(.horizontal)

            // Nutrition Info
            if let nutrition = detail.nutrition {
                VStack(alignment: .leading, spacing: 10) {
                    Text("Nutrition Information")
                        .font(.headline)

                    HStack(spacing: 15) {
                        if let calories = nutrition.calories {
                            NutritionBadge(title: "Calories", value: "\(calories)", color: .orange)
                        }
                        if let protein = nutrition.protein {
                            NutritionBadge(title: "Protein", value: protein, color: .red)
                        }
                        if let carbs = nutrition.carbs {
                            NutritionBadge(title: "Carbs", value: carbs, color: .blue)
                        }
                        if let fat = nutrition.fat {
                            NutritionBadge(title: "Fat", value: fat, color: .green)
                        }
                    }
                }
                .padding(.horizontal)

                Divider()
            }

            // Ingredients Section
            VStack(alignment: .leading, spacing: 10) {
                Text("Ingredients")
                    .font(.headline)

                ForEach(Array(detail.ingredients.enumerated()), id: \.offset) { index, ingredient in
                    HStack {
                        Image(systemName: "circle.fill")
                            .font(.system(size: 6))
                            .foregroundColor(.orange)

                        Text(ingredient)
                            .font(.body)
                    }
                    .padding(.vertical, 2)
                }
            }
            .padding(.horizontal)

            Divider()

            // Instructions Section
            VStack(alignment: .leading, spacing: 15) {
                Text("Instructions")
                    .font(.headline)

                ForEach(Array(detail.steps.enumerated()), id: \.offset) { index, step in
                    HStack(alignment: .top, spacing: 12) {
                        Text("\(index + 1)")
                            .font(.headline)
                            .foregroundColor(.white)
                            .frame(width: 28, height: 28)
                            .background(Circle().fill(Color.orange))

                        Text(step)
                            .font(.body)
                            .fixedSize(horizontal: false, vertical: true)

                        Spacer()
                    }
                }
            }
            .padding(.horizontal)
            .padding(.bottom, 30)
        }
    }

    /// Fallback content using the old Recipe model
    @ViewBuilder
    private func fallbackRecipeContent() -> some View {
        VStack(alignment: .leading, spacing: 20) {
            // Recipe Title and Description
            VStack(alignment: .leading, spacing: 10) {
                Text(fallbackRecipe.recipeTitle)
                    .font(.largeTitle)
                    .fontWeight(.bold)

                Text(fallbackRecipe.description)
                    .font(.body)
                    .foregroundColor(.secondary)
            }
            .padding(.horizontal)

            // Nutrition Info
            VStack(alignment: .leading, spacing: 10) {
                Text("Nutrition Information")
                    .font(.headline)

                HStack(spacing: 15) {
                    NutritionBadge(title: "Calories", value: fallbackRecipe.nutrition.calories, color: .orange)
                    NutritionBadge(title: "Protein", value: fallbackRecipe.nutrition.protein, color: .red)
                    NutritionBadge(title: "Carbs", value: fallbackRecipe.nutrition.carbs, color: .blue)
                    NutritionBadge(title: "Fat", value: fallbackRecipe.nutrition.fat, color: .green)
                }
            }
            .padding(.horizontal)

            Divider()

            // Ingredients Section
            VStack(alignment: .leading, spacing: 10) {
                Text("Ingredients")
                    .font(.headline)

                ForEach(fallbackRecipe.ingredients, id: \.self) { ingredient in
                    HStack {
                        Image(systemName: "circle.fill")
                            .font(.system(size: 6))
                            .foregroundColor(.orange)

                        Text(ingredient)
                            .font(.body)
                    }
                    .padding(.vertical, 2)
                }
            }
            .padding(.horizontal)

            Divider()

            // Instructions Section
            VStack(alignment: .leading, spacing: 15) {
                Text("Instructions")
                    .font(.headline)

                ForEach(Array(fallbackRecipe.instructions.enumerated()), id: \.offset) { index, instruction in
                    HStack(alignment: .top, spacing: 12) {
                        Text("\(index + 1)")
                            .font(.headline)
                            .foregroundColor(.white)
                            .frame(width: 28, height: 28)
                            .background(Circle().fill(Color.orange))

                        Text(instruction)
                            .font(.body)
                            .fixedSize(horizontal: false, vertical: true)

                        Spacer()
                    }
                }
            }
            .padding(.horizontal)
            .padding(.bottom, 30)
        }
    }

    /// Get difficulty color
    private func difficultyColor(_ difficulty: String) -> Color {
        switch difficulty.lowercased() {
        case "easy":
            return .green
        case "medium":
            return .orange
        case "hard":
            return .red
        default:
            return .gray
        }
    }

    /// Build fallback recipe from RecipeUIModel (same logic as RecipesView)
    private func buildRecipe(from item: RecipeUIModel) -> Recipe {
        // Build meaningful description
        let description = if let subtitle = item.subtitle, !subtitle.isEmpty {
            subtitle
        } else {
            "A delicious \(item.title.lowercased()) recipe perfect for any occasion."
        }

        // Combine pantry and additional ingredients
        var allIngredients: [String] = []
        if let pantryIngredients = item.ingredientsFromPantry {
            allIngredients.append(contentsOf: pantryIngredients)
        }
        if let additionalIngredients = item.additionalIngredients {
            allIngredients.append(contentsOf: additionalIngredients)
        }

        // Build instructions with fallback
        let instructions: [String] = if allIngredients.isEmpty {
            [
                "Gather all necessary ingredients for this recipe.",
                "Prepare your cooking area and equipment.",
                "Follow your preferred cooking method for \(item.title.lowercased()).",
                "Cook until done to your liking.",
                "Serve and enjoy!"
            ]
        } else {
            [
                "Gather the following ingredients: \(allIngredients.prefix(3).joined(separator: ", "))\(allIngredients.count > 3 ? " and others" : "").",
                "Prepare your cooking area and wash all fresh ingredients.",
                "Follow your preferred cooking method for \(item.title.lowercased()).",
                "Combine ingredients according to the recipe requirements.",
                "Cook until done, adjusting seasoning as needed.",
                "Serve hot and enjoy your meal!"
            ]
        }

        // Use actual cooking time or default
        let cookingTime = "\(item.estimatedTime ?? 30) minutes"

        // Map difficulty
        let difficulty: Recipe.Difficulty = switch item.difficulty?.lowercased() {
        case "easy": .easy
        case "medium": .medium
        case "hard": .hard
        default: .easy
        }

        return Recipe(
            recipeTitle: item.title,
            description: description,
            ingredients: allIngredients.isEmpty ? ["Ingredients will vary based on your preferences"] : allIngredients,
            instructions: instructions,
            nutrition: .init(calories: "—", protein: "—", carbs: "—", fat: "—"),
            cookingTime: cookingTime,
            servings: item.servings ?? 2,
            difficulty: difficulty
        )
    }
}

struct NutritionBadge: View {
    let title: String
    let value: String
    let color: Color

    var body: some View {
        VStack(spacing: 4) {
            Text(value)
                .font(.headline)
                .foregroundColor(color)
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 8)
        .background(color.opacity(0.1))
        .cornerRadius(8)
    }
}
