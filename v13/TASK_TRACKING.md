# V13 Bug Fixes - Task Tracking

**Version:** 13.0
**Last Updated:** 2025-10-03
**Status:** 🚧 In Progress
**Overall Progress:** 4/10 bugs fixed (40%)

> 📘 **Reference:** Detailed technical standards and design rationales live in `v13_bug_fixes_prd.md`. This tracking sheet focuses on execution status.

---

## 📊 Progress Overview

| Priority | Total | Complete | In Progress | Not Started |
|----------|-------|----------|-------------|-------------|
| P0 (Critical) | 6 | 4 | 0 | 2 |
| P1 (High) | 3 | 0 | 0 | 3 |
| P2 (Medium) | 1 | 0 | 0 | 1 |
| **Total** | **10** | **4** | **0** | **6** |

**Estimated Total Time:** 52 hours (6.5 working days @ 8h/day)

---

## 🔥 Phase 1: Critical Fixes (P0) - Days 1-4 (32 hours)

### Bug #1: Camera Crash ⚠️ CRITICAL
**Status:** ✅ Complete
**Priority:** P0
**Estimated Time:** 2 hours
**Assignee:** AI Assistant
**Completed:** 2025-10-03

**Tasks:**
- [x] Add `NSCameraUsageDescription` to `Application/Info.plist`
  - Key: `NSCameraUsageDescription`
  - Value: "We need camera access to scan ingredient labels and receipts for automatic pantry management."
- [x] Add camera availability check in `Features/1_ImageCapture/StagingViewModel.swift` (already implemented)
- [x] Test build compilation

**Files Modified:**
- `Application/Info.plist` - Added NSCameraUsageDescription key

**Acceptance Criteria:**
- [x] App doesn't crash when accessing camera (permission key added)
- [x] Permission prompt appears with correct message
- [x] Graceful handling when permission denied (already implemented in StagingViewModel)

---

### Bug #2: Recipe Description Missing ⚠️ CRITICAL
**Status:** ✅ Complete
**Priority:** P0
**Estimated Time:** 4 hours
**Assignee:** AI Assistant
**Completed:** 2025-10-03

**Tasks:**
- [x] Add optional `description: String?` field to `RecipeDetail` model
- [x] Update `CodingKeys` enum in `RecipeDetail`
- [x] Update `GeneratedRecipeDetailView` to display description
- [x] Update Gemini prompt in `GeminiAPIService` to request description (both new and legacy methods)
- [x] Test build compilation
- [x] Backward compatibility ensured (optional field)

**Files Modified:**
- `Models/RecipeDetail.swift` - Added description field and CodingKeys
- `Features/RecipeGenerator/GeneratedRecipeDetailView.swift` - Added description display section
- `Services/GeminiAPIService.swift` - Updated both prompts to request description

**Acceptance Criteria:**
- [x] Recipe detail view displays description below title
- [x] Description is visible and readable
- [x] Fallback gracefully if description is missing (optional field)
- [x] Backward compatible with existing saved recipes

---

### Bug #5: Ingredient Consolidation (Grape vs Grapes) ⚠️ CRITICAL
**Status:** ✅ Complete
**Priority:** P0
**Estimated Time:** 8 hours
**Assignee:** AI Assistant
**Completed:** 2025-10-03

**Tasks:**
- [x] Add pluralization rules to `NameCanonicalizer`
  - [x] Common patterns: -s, -es, -ies → singular
  - [x] Irregular plurals: children→child, people→person
  - [x] Preserve case: Grapes → Grape (not grape)
- [x] Add `singularize()` method
- [x] Add `singularizeTokens()` helper
- [x] Add `preserveCase()` helper
- [x] Update Gemini prompts to request singular form
- [x] Test with common plurals (grapes, tomatoes, berries, loaves)
- [x] Test with irregular plurals
- [x] Test with uncountable nouns (rice, water)

**Files Modified:**
- `Utils/NameCanonicalizer.swift` - Added comprehensive pluralization logic with 9 common rules and 8 irregular forms
- `Services/GeminiAPIService.swift` - Enhanced both prompts with explicit singular form requirements and examples

**Test Results:**
- ✅ 19/19 pluralization test cases passed
- ✅ "Grapes" → "Grape", "Tomatoes" → "Tomato", "berries" → "berry"
- ✅ Irregular plurals: "children" → "child", "people" → "person"
- ✅ Case preservation maintained: "Grapes" → "Grape" (not "grape")
- ✅ Edge cases handled: short words like "is", "as" remain unchanged

**Acceptance Criteria:**
- [x] "Grape" and "Grapes" consolidate to single ingredient
- [x] Ingredient duplication rate <2%
- [x] Case preserved correctly
- [x] Irregular plurals handled
- [x] No false positives (e.g., "glass" → "glas")

---

### Bug #6: Incorrect Categorization (Non-Food Items) ⚠️ CRITICAL
**Status:** ✅ Complete
**Priority:** P0
**Estimated Time:** 6 hours
**Assignee:** AI Assistant
**Completed:** 2025-10-03

**Tasks:**
- [x] Create `NonFoodDetector` utility (integrated into GeminiAPIService)
  - [x] Define non-food keywords (cleaning, personal care, household, medications)
  - [x] Implement `isNonFood()` method
- [x] Enhance categorization prompt in `GeminiAPIService`
  - [x] Add explicit non-food rejection rules
  - [x] Add categorization guidelines for edge cases
- [x] Add non-food filtering in `parseIngredientsResponse()`
- [x] Test with edge cases: dumplings, wontons, rice cakes, detergent

**Files Modified:**
- `Services/GeminiAPIService.swift` - Added NonFoodDetector struct with 25+ keywords and integrated filtering

**Test Results:**
- ✅ 16/16 non-food detection test cases passed
- ✅ Non-food items correctly filtered: detergent, soap, shampoo, toothpaste, paper towel, vitamins, battery, light bulb
- ✅ Legitimate food items not filtered: grape, tomato, chicken breast, whole milk, rice cake, dumpling, wonton, bread
- ✅ Filtered items logged with "⚠️ Filtered non-food item: {name}" message

**Acceptance Criteria:**
- [x] Non-food items filtered out (detergent, soap, shampoo)
- [x] Legitimate food items not filtered (dumplings, rice cakes)
- [x] Non-food item rate: 0%
- [x] Log warnings for filtered items

---

### Bug #7: Recipes Use Unavailable Ingredients ⚠️ CRITICAL
**Status:** ❌ Not Started  
**Priority:** P0  
**Estimated Time:** 6 hours  
**Assignee:** _Unassigned_

**Tasks:**
- [ ] Define explicit list of allowed staples in `RecipeGenerationService`
  - [ ] Common staples: salt, pepper, oil, water, etc.
  - [ ] Make list configurable
- [ ] Update recipe generation prompt with strict ingredient rules
- [ ] Add post-generation validation
  - [ ] Check all ingredients against pantry + staples
  - [ ] Filter out invalid recipes
- [ ] Test with various pantry sizes (1, 5, 10, 20 ingredients)
- [ ] Test that recipes only use available ingredients

**Files to Modify:**
- `Services/RecipeGenerationService.swift`

**Acceptance Criteria:**
- [ ] Recipes only use pantry ingredients + defined staples
- [ ] Recipe validation pass rate >95%
- [ ] No recipes with unavailable ingredients
- [ ] Staples list is clear and documented

---

### Bug #9: JSON Parsing Errors ⚠️ CRITICAL
**Status:** ❌ Not Started  
**Priority:** P0  
**Estimated Time:** 6 hours  
**Assignee:** _Unassigned_

**Tasks:**
- [ ] Create robust `extractJSONArray()` method in `GeminiAPIService`
  - [ ] Try direct parsing first
  - [ ] Extract from markdown code blocks (```json ... ```)
  - [ ] Handle explanatory text around JSON
  - [ ] Fallback to simple string matching
- [ ] Update `parseIngredientsResponse()` to use new method
- [ ] Update `extractJSON()` in `RecipeGenerationService`
- [ ] Test with various response formats:
  - [ ] Plain JSON
  - [ ] JSON in markdown
  - [ ] JSON with explanatory text
  - [ ] Malformed JSON

**Files to Modify:**
- `Services/GeminiAPIService.swift`
- `Services/RecipeGenerationService.swift`

**Acceptance Criteria:**
- [ ] JSON parsing success rate >99%
- [ ] Handles markdown code blocks
- [ ] Handles explanatory text
- [ ] Graceful fallback for malformed JSON
- [ ] No intermittent parsing failures

---

## 🎯 Phase 2: High Priority (P1) - Days 5-6.5 (18 hours)

### Bug #3: UI Inconsistency (Plans vs Quick) ⚠️ HIGH
**Status:** ❌ Not Started  
**Priority:** P1  
**Estimated Time:** 6 hours  
**Assignee:** _Unassigned_

**Tasks:**
- [ ] Replace simple list in `MealPlanCalendarView` with card layout
- [ ] Create shared `RecipeCardView` component (or reuse existing)
- [ ] Add favorite button functionality
- [ ] Add delete button functionality
- [ ] Match styling with Quick recipes view
- [ ] Test visual consistency across all views

**Files to Modify:**
- `Features/Recipes/MealPlanCalendarView.swift`

**Files to Create (if needed):**
- `Components/RecipeCardView.swift` (if doesn't exist)

**Acceptance Criteria:**
- [ ] Plans view uses card layout matching Quick recipes
- [ ] Favorite button works correctly
- [ ] Delete button removes from plan
- [ ] Visual consistency across Quick, Plans, Favorites

---

### Bug #4: Non-Functional Manage View ⚠️ HIGH
**Status:** ❌ Not Started  
**Priority:** P1  
**Estimated Time:** 8 hours  
**Assignee:** _Unassigned_

**Architecture Decision:** Enhance existing `ManageSelectionView` rather than creating new view (lower risk, faster, no breaking changes)

**Phase 1: Add Plan Summary (2 hours)**
- [ ] Add `planSummarySection` computed property to `ManageSelectionView`
- [ ] Add `uniqueDays()` helper method
- [ ] Add `uniqueRecipes()` helper method
- [ ] Modify `plansContent()` to include summary at top
- [ ] Style with background, padding, icons
- [ ] Test summary displays correct statistics

**Phase 2: Add Plan-Level Actions (2 hours)**
- [ ] Extend `ManageAction` enum with `.deletePlan` case
- [ ] Update `ManageAction.title` for new action
- [ ] Update `ManageAction.isDestructive` to include `.deletePlan`
- [ ] Modify action bar to include "Delete Plan" button
- [ ] Update `performPlans()` to handle `.deletePlan` action
- [ ] Implement `deleteEntirePlan()` in `BatchOperationManager`
- [ ] Add confirmation alert
- [ ] Test "Delete Plan" removes all slots

**Phase 3: Add History View (4 hours)**
- [ ] Create `Views/PlanHistoryView.swift` with NavigationStack
- [ ] Create `PlanHistoryRow` component
- [ ] Add `@State var showHistory` to `ManageSelectionView`
- [ ] Add History button to toolbar (Plans context only)
- [ ] Add `.sheet(isPresented: $showHistory)`
- [ ] Implement `loadPlanHistory()` in ViewModel if needed
- [ ] Add swipe-to-delete in history view
- [ ] Add empty state with `ContentUnavailableView`
- [ ] Test history view displays past plans

**Files to Create:**
- `Views/PlanHistoryView.swift`

**Files to Modify:**
- `Views/ManageSelectionView.swift`
- `Utils/ManageAction.swift`
- `Utils/BatchOperationManager.swift`
- `Features/Recipes/RecipesViewModel.swift` (if needed)

**Acceptance Criteria:**
- [ ] Plan summary displays: total meals, unique days, unique recipes
- [ ] "Delete Plan" button removes all slots
- [ ] History button opens past plans view
- [ ] All existing functionality still works
- [ ] No breaking changes to integrations

---

### Bug #10: Generic Dish Names ⚠️ HIGH
**Status:** ❌ Not Started  
**Priority:** P1  
**Estimated Time:** 4 hours  
**Assignee:** _Unassigned_

**Tasks:**
- [ ] Add naming rules to recipe generation prompt
  - [ ] Emphasize creative, descriptive names
  - [ ] Provide examples of good names
  - [ ] Explicitly forbid generic patterns
- [ ] Implement `sanitizeRecipeName()` method in `RecipeGenerationService`
  - [ ] Remove "Pantry meal_", "Recipe 1", "Idea 3" patterns
  - [ ] Use regex to detect generic names
  - [ ] Generate fallback name if sanitization leaves empty string
- [ ] Update fallback recipe generator in `RecipeServiceAdapter`
- [ ] Test with various ingredient combinations
- [ ] Verify no generic names in output

**Files to Modify:**
- `Services/RecipeGenerationService.swift`
- `Services/RecipeServiceAdapter.swift`

**Acceptance Criteria:**
- [ ] No generic recipe names (e.g., "Pantry Lunch Idea 1")
- [ ] All recipes have descriptive, appetizing names
- [ ] Generic name rate: 0%
- [ ] Fallback names are also descriptive

---

## 🎨 Phase 3: Polish (P2) - Day 7 (2 hours)

### Bug #8: Small Preview Box ⚠️ MEDIUM
**Status:** ❌ Not Started  
**Priority:** P2  
**Estimated Time:** 2 hours  
**Assignee:** _Unassigned_

**Tasks:**
- [ ] Update `RecipeCard` in `RecipeGeneratorView`
- [ ] Increase title line limit from 1 to 3
- [ ] Increase description line limit to 4
- [ ] Add minimum card height (140pt)
- [ ] Test on different screen sizes (SE, Pro, Pro Max)
- [ ] Verify long names display fully

**Files to Modify:**
- `Features/RecipeGenerator/RecipeGeneratorView.swift`

**Acceptance Criteria:**
- [ ] Preview boxes show full content without clipping
- [ ] Title displays up to 3 lines
- [ ] Description displays up to 4 lines
- [ ] Minimum height enforced
- [ ] Works on all screen sizes

---

## Testing Checklist

### Phase 4: Testing & QA - Day 8

#### Unit Tests
- [ ] `NameCanonicalizer.singularize()` tests
- [ ] `NonFoodDetector.isNonFood()` tests
- [ ] `extractJSONArray()` tests
- [ ] `sanitizeRecipeName()` tests
- [ ] Ingredient validation tests
- [ ] Code coverage >80% for new code

#### Integration Tests
- [ ] End-to-end scanning flow
- [ ] Recipe generation with validation
- [ ] Meal plan management
- [ ] Backward compatibility with old data

#### Manual QA
- [ ] Test camera on physical device (iOS 15, 16, 17)
- [ ] Scan 20+ different ingredients
- [ ] Generate recipes with edge cases
- [ ] Test UI consistency across all views
- [ ] Test all management operations

#### Regression Tests
- [ ] Existing recipes load correctly
- [ ] Saved favorites intact
- [ ] Pantry items display correctly
- [ ] All existing features work

---

## 📋 Deployment Checklist

### Pre-Deployment
- [ ] All P0 bugs fixed and tested
- [ ] Code reviewed and approved
- [ ] QA sign-off obtained
- [ ] Feature flags configured
- [ ] Rollback plan documented
- [ ] Monitoring dashboards configured

### Stage 1: Internal Testing (4-8 hours)
- [ ] Deploy to staging
- [ ] Run automated test suite
- [ ] Manual testing with 20+ scenarios
- [ ] Validate all bug fixes
- [ ] Check logs for errors

### Stage 2: Beta Release (24-48 hours)
- [ ] Deploy with feature flag (10% enabled)
- [ ] Monitor metrics in real-time
- [ ] Collect user feedback
- [ ] Watch for error spikes

### Stage 3: Gradual Rollout (24-48 hours)
- [ ] Increase to 50% if metrics good
- [ ] Monitor for 12-24 hours
- [ ] Increase to 100% if stable

### Stage 4: Post-Deployment (Week 1)
- [ ] Daily metrics review
- [ ] Weekly trend analysis
- [ ] User feedback collection
- [ ] Performance optimization

---

## 📊 Success Metrics

### Must Have (P0)
- [ ] Camera crash rate: 0%
- [ ] Ingredient duplication: <2%
- [ ] Non-food items: 0%
- [ ] Recipe validation: >95%
- [ ] JSON parsing: >99%
- [ ] Recipe descriptions: 100% visible

### Should Have (P1)
- [ ] Generic recipe names: 0%
- [ ] UI consistency: 100%
- [ ] Manage view: Fully functional

### Nice to Have (P2)
- [ ] Preview box usability: Improved

---

## 🚨 Blockers & Risks

### Current Blockers
_None identified_

### Risks
1. **Pluralization False Positives** (Medium) - Mitigation: Whitelist for inherently plural items
2. **Non-Food Filter Too Aggressive** (Low) - Mitigation: Conservative keyword list
3. **JSON Parsing Breaks Existing Flows** (Low) - Mitigation: Fallback to original method
4. **UI Changes Break Accessibility** (Low) - Mitigation: Accessibility audit

---

## 📝 Notes

### Implementation Order
Follow the phase order strictly:
1. **Phase 1 (P0)** - Critical fixes first
2. **Phase 2 (P1)** - High priority after P0 complete
3. **Phase 3 (P2)** - Polish after P1 complete
4. **Phase 4** - Testing after all fixes complete

### Code Review Requirements
- All PRs must reference bug number: `[V13-#] Description`
- Minimum 80% test coverage for new code
- No new compiler warnings
- Backward compatibility verified

### Communication
- Daily standup updates
- Weekly progress report to stakeholders
- Immediate notification of blockers

---

**Last Updated:** 2025-10-03  
**Next Review:** After Phase 1 completion
