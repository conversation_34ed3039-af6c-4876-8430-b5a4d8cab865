## v13 Issues

Here is a cleaned-up and organized list of issues based on user reports.

### 1. Camera Crash Due to Missing Permissions

*   **Problem:** The app crashes when the user tries to access the camera feature.
*   **Analysis:** The application does not prompt the user for camera permissions, causing a crash when camera hardware is initialized.
*   **Affected Code:** Likely related to the initial setup and view loading within the `ImageCapture` feature.

### 2. Recipe Description Missing in Detail View

*   **Problem:** The recipe description, which is generated and shown on recipe cards, is not visible in the detailed recipe view.
*   **Requirement:** A section to display the existing recipe description needs to be added to the recipe detail screen.
*   **Affected Code:** `Features/2_Results`, `Features/3_Results`, and the associated recipe detail views.

### 3. UI Inconsistency in Meal Plan Dish Listing

*   **Problem:** The visual representation of dishes within the "Plans" sub-tab is inconsistent with the card-based layout used in the "Quick" recipes sub-tab.
*   **Requirement:** The UI for listing dishes within a selected meal plan should be updated to match the card layout from "Quick" recipes. This includes displaying the dish name, cooking time, description, ingredients, a "favorite" (heart) button, and a delete button.
*   **Affected Code:** The views responsible for rendering the dish list within the `Recipes` feature's "Plans" sub-tab.

### 4. Non-Functional "Manage" View for Meal Plans

*   **Problem:** In the "Recipes" tab, under the "Plans" sub-tab, the "Manage" button leads to a view where all action buttons are disabled (grayed out) and no meals or dishes are listed.
*   **Requirement:** The logic for the "Manage" view needs to be implemented so that it correctly loads and displays the user's meals and dishes, and the management buttons (e.g., delete, edit) are functional.
*   **Affected Code:** The "Manage" view and its associated logic within the `Recipes` feature's "Plans" sub-tab.

### 5. Ingredient Consolidation Logic Defect

*   **Problem:** The ingredient cleanup and consolidation logic is failing to group similar items. For instance, "Grape" and "Grapes" are treated as distinct ingredients.
*   **Requirement:** The cleanup logic must be made more robust to correctly consolidate ingredients, ensuring consistency whether they are added via the scanner or manually in the pantry.
*   **Affected Code:** `Services/PantryService.swift`, `Services/IngredientLibrary.swift`, `Features/1_ImageCapture`.

### 6. Inconsistent and Incorrect Ingredient Categorization

*   **Problem:** Ingredient categorization is erratic and produces incorrect results. "Pork and leek dumplings" were assigned to different categories at different times. Items like "rice cake" and "mini wontons" are questionably placed in "Others." Non-food items like "laundry detergent" are being added to the pantry.
*   **Requirement:** The categorization logic needs to be investigated. It is recommended to review and potentially revert to a previous, more effective version of the categorization prompt.
*   **Affected Code:** `Services/PantryService.swift`, `Services/GeminiAPIService.swift`.

### 7. Recipe Generation Using Unavailable Ingredients

*   **Problem:** The recipe generation service is creating recipes that require ingredients not available in the user's pantry (e.g., "carrot"), excluding common kitchen staples like oil or salt.
*   **Requirement:** The API call and prompt for detailed recipe generation must be investigated to ensure it strictly adheres to the ingredients provided from the initial recipe idea generation.
*   **Affected Code:** `Services/RecipeGenerationService.swift`, `Services/GeminiAPIService.swift`.

### 8. Quick Generator Preview Box Is Too Small

*   **Problem:** The UI component that previews results from the quick generator is not large enough, preventing users from reading the full dish name and description.
*   **Requirement:** The preview box's height should be increased to show more content at a glance.
*   **Affected Code:** The UI views within the `RecipeGenerator` feature.

### 9. Image Processing JSON Response Error

*   **Problem:** Image processing fails with a "Failed to parse Gemini response" error, indicating that the JSON response is not correctly formatted.
*   **Requirement:** Investigate the Gemini API response and the app's decoding logic to handle any malformed JSON and prevent parsing failures.
*   **Affected Code:** `Services/GoogleVisionAPIService.swift`, `Services/GeminiAPIService.swift`.

### 10. Generic or Numbered Dish Names

*   **Problem:** The recipe generation service sometimes produces generic or numbered dish names, such as "Pantry meal_lunch Idea 3".
*   **Requirement:** The recipe generation prompt and its underlying logic must be investigated to ensure that all generated dishes have descriptive, user-friendly names.
*   **Affected Code:** `Services/RecipeGenerationService.swift`, `Services/GeminiAPIService.swift`.

---

## Developer Notes (re: Issues 9 & 10)

The following debug logs were captured during a session where generic dish names were generated. These logs show a series of **successful** API calls and responses, which contradicts the JSON parsing error reported in **Issue 9**. This suggests the parsing error may be intermittent or triggered by a different, uncaptured workflow.

For **Issue 10**, while the logs confirm API communication, they do not include the actual prompt or response content. To diagnose why generic names are being generated, the full content of the API request and response will need to be inspected.

```
🚀 [Gemini API] Starting request - Prompt: 1111 chars, MaxTokens: 500
📡 [Gemini API] Sending request...
📥 [Gemini API] Response received - Network time: 1.56s, Data size: 2450 bytes
🔍 [Gemini API] Parsing response...
✅ [Gemini API] Success - Parse: 0.00s, Total: 1.56s, Response: 1771 chars
...
(Additional successful calls)
...
✅ Cleared recent ingredient flags
🚀 [Gemini API] Starting request - Prompt: 6161 chars, MaxTokens: 256
📡 [Gemini API] Sending request...
📥 [Gemini API] Response received - Network time: 0.96s, Data size: 1424 bytes
🔍 [Gemini API] Parsing response...
✅ [Gemini API] Success - Parse: 0.00s, Total: 0.96s, Response: 766 chars
```