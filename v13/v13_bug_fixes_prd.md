# V13 Bug Fixes - Product Requirements Document

**Version:** 1.0
**Date:** 2025-10-03
**Status:** 📋 IMPLEMENTATION PLAN (Not Yet Implemented)
**Priority:** P0 - Critical

---

## ⚠️ DOCUMENT STATUS

**This is a DESIGN DOCUMENT and IMPLEMENTATION PLAN.**

The bugs described below have been identified through user testing and thoroughly analyzed. Detailed technical solutions have been designed with code examples. However, **the actual code changes have NOT been implemented yet**.

**Current State:**
- ✅ Bug identification complete (10 bugs found)
- ✅ Root cause analysis complete
- ✅ Technical solutions designed
- ❌ Code implementation pending
- ❌ Testing pending
- ❌ Deployment pending

**Implementation Status: 0/10 bugs fixed (0%)**

> ℹ️ **Technical Standards:** All architecture, coding, and testing standards referenced by V13 are consolidated within this PRD (see "Technical Standards" subsections under each bug and the global standards appendix). The legacy `V13_TECHNICAL_STANDARDS.md` file has been retired.

---

## Executive Summary

This PRD addresses 10 critical bugs identified in user testing for v13 of the Ingredient Scanner app. These issues span camera permissions, UI/UX inconsistencies, data quality problems, and API integration defects. All issues have been analyzed with root cause identification and detailed technical solutions.

**Impact:** These bugs significantly degrade user experience and app stability. Fixing them is essential before any public release.

**Key Finding:** Code review confirms all 10 bugs remain in the current codebase. This document provides the complete implementation plan.

---

## Bug Fixes Overview

| ID | Issue | Priority | Estimated Effort |
|----|-------|----------|------------------|
| 1 | Camera Crash Due to Missing Permissions | P0 | 2 hours |
| 2 | Recipe Description Missing in Detail View | P0 | 4 hours |
| 3 | UI Inconsistency in Meal Plan Dish Listing | P1 | 6 hours |
| 4 | Non-Functional "Manage" View for Meal Plans | P1 | 8 hours |
| 5 | Ingredient Consolidation Logic Defect | P0 | 8 hours |
| 6 | Inconsistent Ingredient Categorization | P0 | 6 hours |
| 7 | Recipe Generation Using Unavailable Ingredients | P0 | 6 hours |
| 8 | Quick Generator Preview Box Too Small | P2 | 2 hours |
| 9 | Image Processing JSON Response Error | P0 | 6 hours |
| 10 | Generic or Numbered Dish Names | P1 | 4 hours |

**Total Estimated Effort:** 52 hours (~6.5 days)

---

## Detailed Bug Fixes

### Bug #1: Camera Crash Due to Missing Permissions

**Priority:** P0 - Critical  
**Severity:** App Crash  
**Affected Users:** All users attempting to scan ingredients

#### Problem Statement
The app crashes immediately when users tap "Scan Receipts and Ingredients" because the required camera permission description is missing from `Info.plist`.

#### Root Cause
iOS requires `NSCameraUsageDescription` in `Info.plist` for any app accessing the camera. Without this key, the system terminates the app immediately upon camera access.

#### Technical Solution

**File:** `Application/Info.plist`

Add the following entry:
```xml
<key>NSCameraUsageDescription</key>
<string>This app uses the camera to scan ingredient labels and receipts for automatic pantry management.</string>
```

**File:** `Features/1_ImageCapture/StagingViewModel.swift`

Add camera availability check before presenting camera UI:
```swift
func scanReceiptsAndIngredients() {
    // Check camera availability
    guard UIImagePickerController.isSourceTypeAvailable(.camera) else {
        // Fallback to photo library on simulator or devices without camera
        showImagePicker = true
        return
    }
    
    // Present camera
    showCamera = true
}
```

#### Acceptance Criteria
- [ ] App does not crash when accessing camera on physical devices
- [ ] Permission prompt displays with the correct message
- [ ] Simulator gracefully falls back to photo library
- [ ] No console warnings about missing permissions

#### Testing Notes
- Must test on physical iOS device (simulator doesn't have camera)
- Test permission denial flow
- Verify permission prompt appears only once

---

### Bug #2: Recipe Description Missing in Detail View

**Priority:** P0 - Critical  
**Severity:** Missing Feature  
**Affected Users:** All users viewing recipe details

#### Problem Statement
Recipe cards display a description (subtitle), but when users tap to view details, the description is not shown. This creates confusion and reduces recipe appeal.

#### Root Cause
The `RecipeDetail` model (returned by Gemini API for detailed recipes) does not include a `description` field. The detail view only displays title, ingredients, steps, and nutrition info.

#### Technical Solution

**File:** `Models/RecipeDetail.swift`

Add `description` field to the model:
```swift
struct RecipeDetail: Codable, Equatable {
    let title: String
    let description: String?  // NEW: Optional description
    let servings: Int
    let totalTimeMinutes: Int
    let difficulty: String
    let ingredients: [String]
    let steps: [String]
    let nutrition: RecipeNutrition?
    
    enum CodingKeys: String, CodingKey {
        case title
        case description  // NEW
        case servings
        case totalTimeMinutes
        case difficulty
        case ingredients
        case steps
        case nutrition
    }
}
```

**File:** `Features/RecipeGenerator/GeneratedRecipeDetailView.swift`

Add description section after title (around line 190):
```swift
VStack(alignment: .leading, spacing: 10) {
    Text(detail.title)
        .font(.largeTitle)
        .fontWeight(.bold)
    
    // NEW: Description section
    if let description = detail.description, !description.isEmpty {
        Text(description)
            .font(.body)
            .foregroundColor(.secondary)
            .padding(.top, 4)
    }
    
    HStack(spacing: 16) {
        Label("\(detail.servings) servings", systemImage: "person.2")
        // ... rest of the code
    }
}
```

**File:** `Services/GeminiAPIService.swift`

Update the recipe detail generation prompt to request description:
```swift
// In the prompt for detailed recipe generation:
"""
Return a JSON object with:
{
  "title": "Recipe Name",
  "description": "Brief 1-2 sentence description of the dish",  // NEW
  "servings": 4,
  "totalTimeMinutes": 30,
  "difficulty": "easy|medium|hard",
  "ingredients": ["item1", "item2"],
  "steps": ["step1", "step2"],
  "nutrition": {"calories": 350, "protein": "25g", "carbs": "30g", "fat": "15g"}
}
"""
```

#### Acceptance Criteria
- [ ] Recipe detail view displays description below title
- [ ] Description is visible and readable
- [ ] Fallback gracefully if description is missing (optional field)
- [ ] Description matches what was shown on the recipe card

#### Testing Notes
- Test with newly generated recipes
- Test with existing saved recipes (should handle missing description)
- Verify description doesn't overflow or clip

---

### Bug #3: UI Inconsistency in Meal Plan Dish Listing

**Priority:** P1 - High  
**Severity:** UX Inconsistency  
**Affected Users:** Users viewing meal plans

#### Problem Statement
The "Plans" sub-tab displays dishes in a simple list format, while the "Quick" recipes sub-tab uses an attractive card-based layout. This inconsistency confuses users and makes the Plans view feel incomplete.

#### Root Cause
`SlotRecipesListView` in `MealPlanCalendarView.swift` uses a basic `List` with `NavigationLink`, while Quick recipes use custom card components with rich metadata display.

#### Technical Solution

**File:** `Features/Recipes/MealPlanCalendarView.swift`

Replace the simple list with card-based layout (lines 54-77):
```swift
private struct SlotRecipesListView: View {
    let date: Date
    let mealType: MealType
    let slots: [MealSlot]
    @State private var viewModel = RecipesViewModel()
    
    var body: some View {
        ScrollView {
            VStack(spacing: 16) {
                // Header
                Text(headerTitle)
                    .font(.title2)
                    .fontWeight(.semibold)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal)
                
                // Card-based layout matching Quick recipes
                ForEach(slots) { slot in
                    NavigationLink(destination: GeneratedRecipeDetailView(recipeUIModel: slot.recipe)) {
                        RecipeCardView(
                            recipe: slot.recipe,
                            isFavorite: viewModel.isFavorite(slot.recipe),
                            onToggleFavorite: { viewModel.toggleFavorite(slot.recipe) },
                            onDelete: { viewModel.deleteFromPlan(slot) }
                        )
                    }
                    .buttonStyle(.plain)
                    .padding(.horizontal)
                }
            }
            .padding(.vertical)
        }
        .navigationTitle(mealType.displayName)
        .onAppear { viewModel.reload() }
    }
    
    private var headerTitle: String {
        let df = DateFormatter()
        df.dateStyle = .medium
        return df.string(from: date)
    }
}

// NEW: Reusable card component
private struct RecipeCardView: View {
    let recipe: RecipeUIModel
    let isFavorite: Bool
    let onToggleFavorite: () -> Void
    let onDelete: () -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(recipe.title)
                        .font(.headline)
                        .foregroundColor(.primary)
                    if let subtitle = recipe.subtitle, !subtitle.isEmpty {
                        Text(subtitle)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .lineLimit(2)
                    }
                }
                Spacer()
            }
            
            HStack {
                if let time = recipe.estimatedTime {
                    Label("\(time) min", systemImage: "clock")
                }
                Spacer()
                if let servings = recipe.servings {
                    Label("\(servings) servings", systemImage: "person.2")
                }
                Spacer()
                if let difficulty = recipe.difficulty {
                    Label(difficulty, systemImage: "star")
                }
            }
            .font(.caption)
            .foregroundColor(.secondary)
            
            HStack {
                Button(action: onToggleFavorite) {
                    Image(systemName: isFavorite ? "heart.fill" : "heart")
                        .foregroundColor(isFavorite ? .red : .gray)
                }
                .buttonStyle(.plain)
                
                Spacer()
                
                Button(action: onDelete) {
                    Label("Delete", systemImage: "trash")
                        .foregroundColor(.red)
                }
                .buttonStyle(.plain)
            }
        }
        .padding()
        .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))
    }
}
```

#### Acceptance Criteria
- [ ] Plans dish list uses card layout matching Quick recipes
- [ ] Cards display: title, description, time, servings, difficulty
- [ ] Favorite button works and shows correct state
- [ ] Delete button removes dish from plan
- [ ] Visual consistency across all recipe views

---

### Bug #4: Non-Functional "Manage" View for Meal Plans

**Priority:** P1 - High
**Severity:** Missing Features
**Affected Users:** Users attempting to manage meal plans

#### Problem Statement
The "Manage" button in the Plans sub-tab leads to a view where all buttons are disabled and no content is displayed. This feature appears to be a stub or incomplete implementation.

#### Root Cause Analysis (Updated 2025-10-03)

**Initial Assessment:** No management view exists.

**Code Review Findings:**
- ✅ `Views/ManageSelectionView.swift` **already exists** (249 lines, fully functional)
- ✅ Supports batch operations: delete, favorite, share, regenerate
- ✅ Integrated into `SwipeableMealPlanCalendarView` (line 58-63)
- ✅ Also used in `QuickHistoryView` for Quick recipes
- ❌ **Missing features:** Plan summary, plan-level operations, history view

**Actual Root Cause:** Existing implementation lacks plan overview and statistics features.

#### Architecture Decision

**Decision:** **Enhance existing `ManageSelectionView`** rather than creating new view.

**Analysis:**
After reviewing the codebase, we found that `Views/ManageSelectionView.swift` (249 lines) already exists and is fully functional:
- ✅ Supports batch operations: delete, favorite, share, regenerate
- ✅ Generic design via `ManageContext` enum (supports both Quick and Plans)
- ✅ Integrated into `SwipeableMealPlanCalendarView` (line 58-63)
- ✅ Also used in `QuickHistoryView` (line 98-101)
- ❌ Missing: Plan summary, plan-level actions, history view

**Rationale for Enhancement Approach:**
- Existing code is functional and well-architected
- Lower risk, faster implementation (8h vs 9+h for creating new view)
- No breaking changes to existing integrations
- Maintains DRY principle (single source of truth)
- Reuses existing `BatchOperationManager`

#### Technical Solution

**Enhance existing `ManageSelectionView` with:**
1. Plan summary section (statistics: meals, days, recipes)
2. Plan-level actions (delete entire plan)
3. History view access
4. Improved UI for plan overview

**Implementation Phases:**

**Phase 1: Add Plan Summary Section (2 hours)**

**File:** `Views/ManageSelectionView.swift` (ENHANCE)

```swift
// ADD: Plan summary section
@ViewBuilder
private var planSummarySection: some View {
    if case .plans(let slots) = context {
        VStack(alignment: .leading, spacing: 8) {
            Text("Plan Summary")
                .font(.headline)

            HStack(spacing: 16) {
                Label("\(slots.count) meals", systemImage: "fork.knife")
                Label("\(uniqueDays(slots)) days", systemImage: "calendar")
                Label("\(uniqueRecipes(slots)) recipes", systemImage: "book")
            }
            .font(.subheadline)
            .foregroundStyle(.secondary)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(8)
        .padding(.horizontal)
        .padding(.bottom, 8)
    }
}

// ADD: Helper methods for statistics
private func uniqueDays(_ slots: [MealSlot]) -> Int {
    Set(slots.compactMap { $0.recipe.scheduledDate }).count
}

private func uniqueRecipes(_ slots: [MealSlot]) -> Int {
    Set(slots.map { $0.recipe.title }).count
}

// MODIFY: plansContent to include summary
private func plansContent(slots: [MealSlot]) -> some View {
    VStack(spacing: 0) {
        planSummarySection  // NEW: Add summary at top

        List {
            // ... existing list code (unchanged)
        }

        actionBar(actions: [.delete, .regenerate, .addToFavorites, .removeFromFavorites, .share], perform: { action in
            Task { await performPlans(action: action, slots: slots) }
        })
    }
}
```

**Phase 2: Add Plan-Level Actions (2 hours)**

**File:** `Utils/ManageAction.swift` (ENHANCE)

```swift
enum ManageAction: Hashable {
    case delete           // Delete selected items
    case deletePlan       // NEW: Delete entire plan
    case addToFavorites
    case removeFromFavorites
    case share
    case regenerate

    var title: String {
        switch self {
        case .delete: return "Delete"
        case .deletePlan: return "Delete Plan"  // NEW
        case .addToFavorites: return "Favorite"
        case .removeFromFavorites: return "Unfavorite"
        case .share: return "Share"
        case .regenerate: return "Regenerate"
        }
    }

    var isDestructive: Bool {
        self == .delete || self == .deletePlan  // MODIFIED
    }
}
```

**File:** `Views/ManageSelectionView.swift` (ENHANCE)

```swift
// MODIFY: Action bar to include new actions
actionBar(actions: [.delete, .deletePlan, .regenerate, .addToFavorites, .share], perform: { action in
    Task { await performPlans(action: action, slots: slots) }
})

// MODIFY: Handle new actions
private func performPlans(action: ManageAction, slots: [MealSlot]) async {
    switch action {
    case .deletePlan:
        // Delete all slots in the plan
        let result = await BatchOperationManager.deleteEntirePlan(slots: slots)
        presentToast(message: result.message, type: toastType(for: result))
        onCompleted?()
        dismiss()  // Close view after deleting plan

    default:
        // Existing logic for other actions
        let selected = slots.filter { selectedSlots.contains($0.slotId) }
        let result = await BatchOperationManager.performPlans(action: action, selectedSlots: selected)
        presentToast(message: result.message, type: toastType(for: result))
        onCompleted?()
    }
}
```

**Phase 3: Add History View (4 hours)**

**File:** `Views/PlanHistoryView.swift` (NEW - Separate view)

```swift
import SwiftUI

struct PlanHistoryView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var viewModel: RecipesViewModel

    init(viewModel: RecipesViewModel = RecipesViewModel()) {
        _viewModel = State(initialValue: viewModel)
    }

    var body: some View {
        NavigationStack {
            List {
                if viewModel.planHistory.isEmpty {
                    ContentUnavailableView(
                        "No Plan History",
                        systemImage: "calendar.badge.clock",
                        description: Text("Your past meal plans will appear here")
                    )
                } else {
                    ForEach(viewModel.planHistory) { plan in
                        PlanHistoryRow(plan: plan)
                            .swipeActions {
                                Button(role: .destructive) {
                                    viewModel.deletePlan(plan)
                                } label: {
                                    Label("Delete", systemImage: "trash")
                                }
                            }
                    }
                }
            }
            .navigationTitle("Plan History")
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Close") { dismiss() }
                }
            }
            .onAppear { viewModel.loadPlanHistory() }
        }
    }
}

private struct PlanHistoryRow: View {
    let plan: WeeklyMealPlan

    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text(formatDateRange(plan.weekStart, plan.weekEnd))
                .font(.headline)
            HStack(spacing: 12) {
                Label("\(plan.totalMeals) meals", systemImage: "fork.knife")
                Label("\(plan.daysCount) days", systemImage: "calendar")
            }
            .font(.caption)
            .foregroundStyle(.secondary)
        }
        .padding(.vertical, 4)
    }

    private func formatDateRange(_ start: Date, _ end: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        return "\(formatter.string(from: start)) - \(formatter.string(from: end))"
    }
}
```

**File:** `Views/ManageSelectionView.swift` (ENHANCE)

```swift
// ADD: State for history sheet
@State private var showHistory: Bool = false

// MODIFY: Toolbar to add history button
.toolbar {
    ToolbarItem(placement: .cancellationAction) {
        Button("Close") { dismiss() }
    }

    // NEW: History button for Plans context
    if case .plans = context {
        ToolbarItem(placement: .primaryAction) {
            Button {
                showHistory = true
            } label: {
                Label("History", systemImage: "clock")
            }
        }
    }

    ToolbarItem(placement: .confirmationAction) {
        if selectionCount > 0 {
            Text("\(selectionCount) selected")
                .font(.subheadline)
                .foregroundStyle(.secondary)
        }
    }
}
.sheet(isPresented: $showHistory) {
    PlanHistoryView()
}
```

**Note:** No changes needed to `PlansHistoryView.swift` or `SwipeableMealPlanCalendarView.swift` as the Manage button already exists and calls `ManageSelectionView`.

#### Acceptance Criteria
- [ ] Plan summary displays total meals, days, and unique recipes
- [ ] "Delete Plan" button removes all slots for the current plan
- [ ] History button opens `PlanHistoryView` with past plans
- [ ] All existing functionality (delete, favorite, share, regenerate) still works
- [ ] No breaking changes to existing integrations
- [ ] Summary section only appears for Plans context (not Quick)

#### Testing Notes
- Test plan summary calculations with various plan sizes
- Test "Delete Plan" action removes all slots
- Test history view displays past plans correctly
- Regression test: Verify Quick recipes management still works
- Regression test: Verify existing Plans management actions work

#### Data Migration
**Required:** None - This is an enhancement, not a data model change.

---

**OLD IMPLEMENTATION (Deprecated):**

The following code was in the original documentation but is replaced by the enhancement approach above:

```swift
// DEPRECATED: Do not create new PlanManagementView
// Instead, enhance existing ManageSelectionView

var body: some View {
    Group {
        if let plan = viewModel.lastMealPrep?.plan {
            MealPlanCalendarView(plan: plan)
                .toolbar {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        NavigationLink("Manage") {
                            PlanManagementView(viewModel: viewModel)
                        }
                    }
                }
        } else {
            PlansEmptyStateView()
        }
    }
    .onAppear { viewModel.reload() }
}
```

**File:** `Features/Recipes/RecipesViewModel.swift`

Add management methods:
```swift
@Observable
@MainActor
class RecipesViewModel {
    // ... existing properties
    
    var planHistory: [MealPlan] = []
    
    func deletePlan(_ plan: MealPlan) {
        // Remove from storage
        if lastMealPrep?.plan.id == plan.id {
            lastMealPrep = nil
        }
        planHistory.removeAll { $0.id == plan.id }
        // Persist changes
        savePlans()
    }
    
    func deleteFromPlan(_ slot: MealSlot) {
        // Remove specific slot from plan
        guard var plan = lastMealPrep?.plan else { return }
        // ... implementation
        savePlans()
    }
    
    private func savePlans() {
        // Persist to storage
    }
}
```

**Option B: Remove/Hide Manage Button**

If management features are not ready, hide the button until implementation is complete.

#### Acceptance Criteria
- [ ] Manage button navigates to functional view
- [ ] Current plan is displayed with summary
- [ ] Delete plan button works correctly
- [ ] Plan history is accessible
- [ ] All buttons are enabled and functional

---

### Bug #5: Ingredient Consolidation Logic Defect

**Priority:** P0 - Critical  
**Severity:** Data Quality Issue  
**Affected Users:** All users managing pantry

#### Problem Statement
The ingredient consolidation logic fails to recognize that "Grape" and "Grapes" are the same ingredient, creating duplicate entries. This affects both scanned and manually added ingredients.

#### Root Cause
The `NameCanonicalizer` handles capitalization, punctuation, and whitespace but does not normalize singular/plural forms. The Gemini API prompt mentions pluralization but relies on AI judgment, which is inconsistent.

#### Technical Solution

**File:** `Utils/NameCanonicalizer.swift`

Add pluralization normalization:
```swift
struct NameCanonicalizer {
    private let stopwords: Set<String> = ["and","or","of","the","a","an","in"]
    
    // NEW: Common plural patterns
    private let pluralRules: [(suffix: String, replacement: String)] = [
        ("ies", "y"),      // berries -> berry
        ("ves", "f"),      // loaves -> loaf
        ("oes", "o"),      // tomatoes -> tomato
        ("ses", "s"),      // glasses -> glass
        ("xes", "x"),      // boxes -> box
        ("ches", "ch"),    // peaches -> peach
        ("shes", "sh"),    // dishes -> dish
        ("s", "")          // grapes -> grape (fallback)
    ]
    
    func canonicalize(_ input: String, locale: Locale = .current) -> String {
        let nfc = input.precomposedStringWithCanonicalMapping.trimmingCharacters(in: CharacterSet.whitespacesAndNewlines)
        guard !nfc.isEmpty else { return "" }
        
        let segments = splitByParentheses(nfc)
        var parts: [String] = []
        
        for seg in segments {
            if seg.inParens {
                parts.append("(\(seg.text))")
            } else {
                let replaced = replaceDashesAndSlashesWithSpace(seg.text)
                let sanitized = removeDisallowedPunctuation(replaced)
                let tokenized = tokenizeKeepingCommas(sanitized)
                let titled = titleCaseTokens(tokenized, locale: locale)
                let singularized = singularizeTokens(titled)  // NEW
                parts.append(joinTokensWithCommas(singularized))
            }
        }
        
        let joined = parts.joined(separator: " ")
        return collapseWhitespace(joined).trimmingCharacters(in: CharacterSet.whitespacesAndNewlines)
    }
    
    // NEW: Singularize tokens
    private func singularizeTokens(_ tokens: [Token]) -> [Token] {
        return tokens.map { token in
            switch token {
            case .word(let w):
                return .word(singularize(w))
            case .comma:
                return .comma
            }
        }
    }
    
    // NEW: Convert plural to singular
    private func singularize(_ word: String) -> String {
        let lower = word.lowercased()
        
        // Skip if too short or already singular
        guard lower.count > 3 else { return word }
        
        // Check against common irregular plurals
        let irregulars: [String: String] = [
            "children": "child",
            "people": "person",
            "men": "man",
            "women": "woman",
            "teeth": "tooth",
            "feet": "foot",
            "mice": "mouse",
            "geese": "goose"
        ]
        
        if let singular = irregulars[lower] {
            return preserveCase(original: word, template: singular)
        }
        
        // Apply suffix rules
        for rule in pluralRules {
            if lower.hasSuffix(rule.suffix) {
                let base = String(lower.dropLast(rule.suffix.count))
                let singular = base + rule.replacement
                return preserveCase(original: word, template: singular)
            }
        }
        
        return word
    }
    
    // NEW: Preserve original capitalization pattern
    private func preserveCase(original: String, template: String) -> String {
        if original.first?.isUppercase == true {
            return template.prefix(1).uppercased() + template.dropFirst()
        }
        return template
    }
    
    // ... rest of existing methods
}
```

**File:** `Services/GeminiAPIService.swift`

Strengthen the pluralization instruction in prompts (lines 46, 91):
```swift
// Update prompt:
"""
2. CRITICAL: Normalize pluralization to SINGULAR form:
   - "tomatoes" -> "Tomato"
   - "grapes" -> "Grape"  
   - "berries" -> "Berry"
   - "loaves" -> "Loaf"
   Always use singular form unless the ingredient is inherently plural (e.g., "Noodles", "Oats").
"""
```

#### Acceptance Criteria
- [ ] "Grape" and "Grapes" consolidate to same ingredient
- [ ] Common plurals (tomatoes, berries, etc.) normalize correctly
- [ ] Irregular plurals (children, people) handled
- [ ] Inherently plural items (noodles, oats) preserved
- [ ] Existing ingredients updated via migration

#### Testing Notes
- Test with common ingredients: apple/apples, tomato/tomatoes, berry/berries
- Test edge cases: rice (uncountable), scissors (inherently plural)
- Verify no false positives (e.g., "glass" vs "glasses" as eyewear)

---

### Bug #6: Inconsistent and Incorrect Ingredient Categorization

**Priority:** P0 - Critical  
**Severity:** Data Quality Issue  
**Affected Users:** All users scanning or adding ingredients

#### Problem Statement
Ingredient categorization is erratic:
- "Pork and leek dumplings" assigned to different categories at different times
- "Rice cake" and "mini wontons" placed in "Others"
- Non-food items like "laundry detergent" being added to pantry

#### Root Cause
Categorization relies entirely on Gemini AI's judgment without:
1. Deterministic rules for common items
2. Non-food item filtering
3. Validation against known food databases

#### Technical Solution

**File:** `Services/GeminiAPIService.swift`

Enhance categorization prompt and add validation (lines 40-60):
```swift
private func buildCanonicalizePrompt(visionOutputs: [VisionOutput], allowedCategories: [String]) -> String {
    var prompt = """
    You are a food ingredient analyzer. Process the following OCR text and labels from images of food items, receipts, or packaging.
    
    CRITICAL RULES:
    1. ONLY process food items. REJECT these non-food categories:
       - Cleaning products (detergent, soap, bleach, etc.)
       - Personal care (shampoo, toothpaste, etc.)
       - Household items (paper towels, batteries, etc.)
       - Medications and supplements
       
    2. For each FOOD item:
       - Extract the ingredient name, removing brands, sizes, quantities
       - Normalize to singular form
       - Categorize into EXACTLY ONE category: \(allowedCategories.joined(separator: ", "))
       
    3. Categorization guidelines:
       - Dumplings, wontons, buns → Frozen Foods (if frozen) or Grains, Pasta & Legumes (if fresh)
       - Rice cakes, mochi → Grains, Pasta & Legumes
       - Prepared meals → Frozen Foods or Canned & Broths (based on packaging)
       - When uncertain, prefer more specific category over "Others"
       
    4. If an item is clearly non-food, OMIT it entirely from output
    
    Return ONLY a JSON array: [{"name":"ingredient name","category":"exact category"}]
    No explanations or additional text.
    """
    
    for (index, output) in visionOutputs.enumerated() {
        prompt += "\n\nIMAGE \(index + 1):\nOCR TEXT: \(output.ocrText)\nLABELS: \(output.labels.joined(separator: ", "))"
    }
    
    return prompt
}
```

**File:** `Services/GeminiAPIService.swift`

Add non-food filtering (after line 155):
```swift
private func parseIngredientsResponse(_ response: String, allowedCategories: [String]) throws -> [Ingredient] {
    // ... existing JSON parsing code
    
    let canonicalizer = NameCanonicalizer()
    let nonFoodDetector = NonFoodDetector()  // NEW
    
    let validIngredients = geminiIngredients.compactMap { geminiIngredient -> Ingredient? in
        let trimmed = geminiIngredient.name.trimmingCharacters(in: .whitespacesAndNewlines)
        let cleanedName = canonicalizer.canonicalize(trimmed)
        
        // NEW: Filter non-food items
        guard !nonFoodDetector.isNonFood(cleanedName) else {
            print("⚠️ Filtered non-food item: \(cleanedName)")
            return nil
        }
        
        guard isValidIngredientName(cleanedName) else { return nil }
        guard allowedCategories.contains(geminiIngredient.category) else { return nil }
        guard let category = PantryCategory(rawValue: geminiIngredient.category) else { return nil }
        
        return Ingredient(id: UUID(), name: cleanedName, category: category)
    }
    
    return validIngredients
}
```

**File:** `Utils/NonFoodDetector.swift` (NEW)

```swift
import Foundation

struct NonFoodDetector {
    private let nonFoodKeywords: Set<String> = [
        // Cleaning
        "detergent", "soap", "cleaner", "bleach", "disinfectant", "wipes",
        // Personal care
        "shampoo", "conditioner", "toothpaste", "deodorant", "lotion",
        // Household
        "paper towel", "tissue", "napkin", "foil", "wrap", "bag",
        // Medications
        "medicine", "pill", "tablet", "vitamin", "supplement",
        // Other
        "battery", "light bulb", "pen", "pencil"
    ]
    
    func isNonFood(_ name: String) -> Bool {
        let lower = name.lowercased()
        return nonFoodKeywords.contains(where: { lower.contains($0) })
    }
}
```

#### Acceptance Criteria
- [ ] Non-food items are filtered out
- [ ] Dumplings, wontons consistently categorized
- [ ] Rice cakes placed in appropriate category
- [ ] "Others" category used <5% of the time
- [ ] Categorization is deterministic for same input

---

### Bug #7: Recipe Generation Using Unavailable Ingredients

**Priority:** P0 - Critical  
**Severity:** Logic Error  
**Affected Users:** All users generating recipes

#### Problem Statement
Generated recipes include ingredients not in the user's pantry (e.g., "carrot" when user doesn't have carrots), while excluding common staples like oil or salt.

#### Root Cause
The recipe generation prompt says "Use provided ingredients + staples" but doesn't:
1. Strictly enforce using ONLY provided ingredients
2. Define what "staples" means
3. Validate generated recipes against pantry

#### Technical Solution

**File:** `Services/RecipeGenerationService.swift`

Update prompt to be more strict (lines 104-160):
```swift
private func createRecipePrompt(from ingredients: [String], preferences: RecipePreferences) -> String {
    let dishTarget = max(1, min(12, preferences.targetDishCount ?? 3))
    
    // Define allowed staples explicitly
    let allowedStaples = [
        "salt", "black pepper", "white pepper",
        "vegetable oil", "olive oil", "cooking oil",
        "water", "ice"
    ]
    
    var constraints: [String] = []
    constraints.append("Return at least \(dishTarget) distinct recipes.")
    
    // ... existing dietary restrictions code
    
    // NEW: Strict ingredient enforcement
    let ingredientList = ingredients.map { "- \($0)" }.joined(separator: "\n")
    let staplesList = allowedStaples.joined(separator: ", ")
    
    let constraintsText = constraints.isEmpty ? "" : """
    
    Constraints:
    \(constraints.map { "- \($0)" }.joined(separator: "\n"))
    """
    
    return """
    Generate \(dishTarget) recipes using ONLY the ingredients listed below.
    
    AVAILABLE INGREDIENTS:
    \(ingredientList)
    
    ALLOWED STAPLES (you may use these even if not listed):
    \(staplesList)
    
    CRITICAL RULES:
    1. Use ONLY ingredients from the available list above
    2. You MAY use the allowed staples without listing them
    3. DO NOT use any other ingredients (e.g., if carrot is not listed, DO NOT use it)
    4. If you cannot make a good recipe with available ingredients, use creative combinations
    5. Each recipe must use at least 2 ingredients from the available list
    
    Servings: \(preferences.numberOfServings)
    Time limit: ~\(preferences.cookingTimeInMinutes) minutes
    \(constraintsText)
    
    JSON format:
    [{"title":"Recipe Name","description":"Brief","ingredients":["item1","item2"],"instructions":["step1","step2"],"cookingTime":"30 minutes","servings":\(preferences.numberOfServings),"difficulty":"easy|medium|hard","nutrition":{"calories":"350","protein":"25g","carbs":"30g","fat":"15g"}}]
    
    Remember: ONLY use ingredients from the available list + allowed staples. No exceptions.
    """
}
```

**File:** `Services/RecipeGenerationService.swift`

Add post-generation validation (after line 72):
```swift
func generateMealIdeas(from ingredients: [String], preferences: RecipePreferences) async throws -> [RecipeIdea] {
    // ... existing generation code
    
    // NEW: Validate recipes use only available ingredients
    let allowedStaples = Set(["salt", "pepper", "oil", "water", "ice"])
    let availableIngredients = Set(ingredients.map { $0.lowercased() })
    
    let validatedRecipes = recipes.filter { recipe in
        let usedIngredients = Set(recipe.ingredients.map { extractIngredientName($0).lowercased() })
        let unavailable = usedIngredients.subtracting(availableIngredients).subtracting(allowedStaples)
        
        if !unavailable.isEmpty {
            print("⚠️ Recipe '\(recipe.title)' uses unavailable ingredients: \(unavailable.joined(separator: ", "))")
            return false
        }
        return true
    }
    
    // ... rest of code
    
    return validatedRecipes.map { recipe in
        RecipeIdea(recipe: recipe, status: .readyToCook, missingIngredients: [])
    }
}

// NEW: Extract ingredient name from formatted string
private func extractIngredientName(_ formatted: String) -> String {
    // Remove quantities and measurements: "2 cups flour" -> "flour"
    let words = formatted.split(separator: " ")
    // Skip numbers and common measurements
    let measurements = Set(["cup", "cups", "tbsp", "tsp", "oz", "lb", "g", "kg", "ml", "l"])
    let filtered = words.filter { word in
        !Double(word).map({ _ in true }) ?? false &&
        !measurements.contains(String(word).lowercased())
    }
    return filtered.joined(separator: " ")
}
```

#### Acceptance Criteria
- [ ] Generated recipes use ONLY pantry ingredients + defined staples
- [ ] No recipes with unavailable ingredients
- [ ] Staples (oil, salt, pepper, water) allowed without being in pantry
- [ ] Validation logs warn about filtered recipes
- [ ] At least 80% of generated recipes pass validation

---

### Bug #8: Quick Generator Preview Box Too Small

**Priority:** P2 - Medium  
**Severity:** UX Issue  
**Affected Users:** Users using quick recipe generator

#### Problem Statement
The preview box showing generated recipe ideas is too small, cutting off dish names and descriptions, forcing users to tap to see full content.

#### Root Cause
The `RecipeCard` component has fixed height constraints or `lineLimit` that truncates content too aggressively.

#### Technical Solution

**File:** `Features/RecipeGenerator/RecipeGeneratorView.swift`

Update RecipeCard layout (lines 617-654):
```swift
struct RecipeCard: View {
    let recipe: Recipe
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 12) {
                HStack(alignment: .top) {  // Changed from default to .top
                    VStack(alignment: .leading, spacing: 6) {  // Increased spacing
                        Text(recipe.title)
                            .font(.headline)
                            .foregroundColor(.primary)
                            .multilineTextAlignment(.leading)
                            .lineLimit(3)  // Increased from implicit 1
                            .fixedSize(horizontal: false, vertical: true)
                        
                        Text(recipe.description)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .lineLimit(4)  // Increased from 2
                            .fixedSize(horizontal: false, vertical: true)
                    }
                    Spacer()
                    Image(systemName: "chevron.right")
                        .foregroundColor(.secondary)
                        .padding(.top, 4)
                }
                
                Divider()  // NEW: Visual separator
                
                HStack(spacing: 16) {  // Increased spacing
                    Label("\(recipe.cookingTimeInMinutes) min", systemImage: "clock")
                    Label("\(recipe.numberOfServings) servings", systemImage: "person.2")
                    Label(recipe.difficulty.rawValue, systemImage: "star")
                }
                .font(.caption)
                .foregroundColor(.secondary)
            }
            .padding(16)  // Increased from default
            .frame(minHeight: 140)  // NEW: Minimum height
            .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))
        }
        .buttonStyle(.plain)
    }
}
```

#### Acceptance Criteria
- [ ] Recipe cards show at least 3 lines of title
- [ ] Description shows at least 4 lines
- [ ] No content is cut off unexpectedly
- [ ] Cards have consistent minimum height
- [ ] Layout remains responsive on different screen sizes

---

### Bug #9: Image Processing JSON Response Error

**Priority:** P0 - Critical  
**Severity:** Intermittent Failure  
**Affected Users:** Users scanning ingredients

#### Problem Statement
Image processing occasionally fails with "Failed to parse Gemini response" error, indicating malformed JSON. The error is intermittent, making it hard to reproduce.

#### Root Cause
The `extractJSON` method in `RecipeGenerationService` and JSON parsing in `GeminiAPIService` use simple string matching (`firstIndex(of: "[")`) which can fail if:
1. AI includes JSON in markdown code blocks
2. Response has multiple JSON arrays
3. Response includes explanatory text before/after JSON

#### Technical Solution

**File:** `Services/GeminiAPIService.swift`

Improve JSON extraction (add new method around line 156):
```swift
/// Robust JSON extraction from AI response
private func extractJSONArray(from response: String) -> Data? {
    // Try direct parsing first
    if let data = response.data(using: .utf8),
       let _ = try? JSONSerialization.jsonObject(with: data) as? [[String: Any]] {
        return data
    }
    
    // Try extracting from markdown code blocks
    let patterns = [
        "```json\\s*\\n(.+?)\\n```",  // ```json ... ```
        "```\\s*\\n(.+?)\\n```",      // ``` ... ```
        "\\[\\s*\\{.+?\\}\\s*\\]"     // [ { ... } ]
    ]
    
    for pattern in patterns {
        if let regex = try? NSRegularExpression(pattern: pattern, options: [.dotMatchesLineSeparators]),
           let match = regex.firstMatch(in: response, range: NSRange(response.startIndex..., in: response)) {
            
            let matchRange = match.range(at: match.numberOfRanges > 1 ? 1 : 0)
            if let swiftRange = Range(matchRange, in: response) {
                let extracted = String(response[swiftRange])
                if let data = extracted.data(using: .utf8),
                   let _ = try? JSONSerialization.jsonObject(with: data) as? [[String: Any]] {
                    return data
                }
            }
        }
    }
    
    // Fallback: find first [ to last ]
    guard let startIndex = response.firstIndex(of: "["),
          let endIndex = response.lastIndex(of: "]") else {
        return nil
    }
    
    let jsonString = String(response[startIndex...endIndex])
    return jsonString.data(using: .utf8)
}
```

Update `parseIngredientsResponse` to use robust extraction (line 105):
```swift
private func parseIngredientsResponse(_ response: String, allowedCategories: [String]) throws -> [Ingredient] {
    // Use robust JSON extraction
    guard let jsonData = extractJSONArray(from: response) else {
        print("❌ Failed to extract JSON from response:")
        print(response.prefix(500))  // Log first 500 chars for debugging
        throw GeminiError.invalidResponseWithMessage("Could not extract JSON array from response")
    }
    
    // ... rest of parsing code
}
```

**File:** `Services/RecipeGenerationService.swift`

Update `extractJSON` method (line 162):
```swift
private func extractJSON(from text: String) -> Data? {
    // Try direct parsing
    if let data = text.data(using: .utf8),
       let _ = try? JSONDecoder().decode([RecipeData].self, from: data) {
        return data
    }
    
    // Try extracting from markdown
    let patterns = [
        "```json\\s*\\n(.+?)\\n```",
        "```\\s*\\n(.+?)\\n```",
        "\\[\\s*\\{.+?\\}\\s*\\]"
    ]
    
    for pattern in patterns {
        if let regex = try? NSRegularExpression(pattern: pattern, options: [.dotMatchesLineSeparators]),
           let match = regex.firstMatch(in: text, range: NSRange(text.startIndex..., in: text)) {
            
            let matchRange = match.range(at: match.numberOfRanges > 1 ? 1 : 0)
            if let swiftRange = Range(matchRange, in: text) {
                let extracted = String(text[swiftRange])
                if let data = extracted.data(using: .utf8),
                   let _ = try? JSONDecoder().decode([RecipeData].self, from: data) {
                    return data
                }
            }
        }
    }
    
    // Fallback
    guard let startIndex = text.firstIndex(of: "["),
          let endIndex = text.lastIndex(of: "]") else {
        print("❌ No JSON array found in response")
        return nil
    }
    
    let jsonString = String(text[startIndex...endIndex])
    return jsonString.data(using: .utf8)
}
```

Add better error logging:
```swift
func generateMealIdeas(from ingredients: [String], preferences: RecipePreferences) async throws -> [RecipeIdea] {
    let prompt = createRecipePrompt(from: ingredients, preferences: preferences)
    let dishCount = max(1, min(12, preferences.targetDishCount ?? 3))
    
    let jsonResponse = try await processRecipeText(prompt, dishCount: dishCount)
    
    guard let jsonData = extractJSON(from: jsonResponse) else {
        print("❌ JSON extraction failed. Response preview:")
        print(jsonResponse.prefix(1000))
        throw RecipeGenerationError.invalidJSONResponse
    }
    
    do {
        let recipeData = try JSONDecoder().decode([RecipeData].self, from: jsonData)
        // ... rest of code
    } catch {
        print("❌ JSON decoding failed: \(error)")
        if let jsonString = String(data: jsonData, encoding: .utf8) {
            print("Attempted to decode:")
            print(jsonString.prefix(1000))
        }
        throw RecipeGenerationError.invalidJSONResponse
    }
}
```

#### Acceptance Criteria
- [ ] JSON parsing succeeds even with markdown formatting
- [ ] Handles responses with explanatory text
- [ ] Logs helpful debugging info on failure
- [ ] Success rate >99% (vs current intermittent failures)
- [ ] Graceful error messages to user

---

### Bug #10: Generic or Numbered Dish Names

**Priority:** P1 - High  
**Severity:** UX Issue  
**Affected Users:** Users generating recipes

#### Problem Statement
Recipe generation sometimes produces generic names like "Pantry meal_lunch Idea 3" instead of descriptive, appetizing names like "Garlic Butter Pasta" or "Spicy Chicken Stir-Fry".

#### Root Cause
The recipe generation prompt doesn't emphasize creative, descriptive naming. The AI may be defaulting to placeholder-style names when uncertain.

#### Technical Solution

**File:** `Services/RecipeGenerationService.swift`

Enhance prompt with naming guidelines (line 104):
```swift
private func createRecipePrompt(from ingredients: [String], preferences: RecipePreferences) -> String {
    // ... existing code
    
    return """
    Generate \(dishTarget) recipes using the ingredients below.
    
    AVAILABLE INGREDIENTS:
    \(ingredients.map { "- \($0)" }.joined(separator: "\n"))
    
    RECIPE NAMING RULES:
    1. Create DESCRIPTIVE, APPETIZING names (e.g., "Garlic Butter Pasta", "Spicy Chicken Stir-Fry")
    2. DO NOT use generic names like "Meal 1", "Lunch Idea", "Pantry Recipe"
    3. DO NOT include numbers in names (e.g., "Recipe 3")
    4. Include key ingredients or cooking method in the name
    5. Keep names concise (2-5 words)
    6. Make names sound delicious and inviting
    
    GOOD EXAMPLES:
    - "Honey Garlic Chicken"
    - "Creamy Tomato Pasta"
    - "Sesame Ginger Stir-Fry"
    
    BAD EXAMPLES (DO NOT USE):
    - "Pantry meal_lunch Idea 3"
    - "Recipe 1"
    - "Dinner Option"
    
    Servings: \(preferences.numberOfServings)
    Time: ~\(preferences.cookingTimeInMinutes) minutes
    \(constraintsText)
    
    JSON format:
    [{"title":"Descriptive Recipe Name","description":"Brief description","ingredients":["item1","item2"],"instructions":["step1","step2"],"cookingTime":"30 minutes","servings":\(preferences.numberOfServings),"difficulty":"easy|medium|hard","nutrition":{"calories":"350","protein":"25g","carbs":"30g","fat":"15g"}}]
    """
}
```

Add post-generation name validation (after line 36):
```swift
func generateMealIdeas(from ingredients: [String], preferences: RecipePreferences) async throws -> [RecipeIdea] {
    // ... existing generation code
    
    var recipes = recipeData.map { data in
        Recipe(
            recipeTitle: sanitizeRecipeName(data.title),  // NEW: Sanitize names
            description: data.description,
            ingredients: data.ingredients,
            // ... rest of fields
        )
    }
    
    // ... rest of code
}

// NEW: Sanitize and validate recipe names
private func sanitizeRecipeName(_ name: String) -> String {
    var sanitized = name.trimmingCharacters(in: .whitespacesAndNewlines)
    
    // Remove common generic patterns
    let genericPatterns = [
        "Pantry meal_",
        "Pantry recipe",
        "Meal \\d+",
        "Recipe \\d+",
        "Idea \\d+",
        "Option \\d+"
    ]
    
    for pattern in genericPatterns {
        if let regex = try? NSRegularExpression(pattern: pattern, options: .caseInsensitive) {
            sanitized = regex.stringByReplacingMatches(
                in: sanitized,
                range: NSRange(sanitized.startIndex..., in: sanitized),
                withTemplate: ""
            ).trimmingCharacters(in: .whitespacesAndNewlines)
        }
    }
    
    // If name is now empty or too short, generate a fallback
    if sanitized.count < 3 {
        sanitized = "Delicious Homemade Dish"
    }
    
    return sanitized
}
```

#### Acceptance Criteria
- [ ] No recipe names contain numbers (e.g., "Idea 3")
- [ ] No generic patterns like "Pantry meal_lunch"
- [ ] All names are descriptive and appetizing
- [ ] Names are 2-5 words long
- [ ] Names reflect key ingredients or cooking method

---

## Implementation Plan

### Phase 1: Critical Fixes (P0) - Days 1-3
1. Bug #1: Camera Permissions (2 hours)
2. Bug #5: Ingredient Consolidation (8 hours)
3. Bug #6: Categorization (6 hours)
4. Bug #7: Recipe Ingredients (6 hours)
5. Bug #9: JSON Parsing (6 hours)
6. Bug #2: Recipe Description (4 hours)

### Phase 2: High Priority (P1) - Days 4-5
7. Bug #10: Dish Names (4 hours)
8. Bug #3: UI Consistency (6 hours)
9. Bug #4: Manage View (8 hours)

### Phase 3: Medium Priority (P2) - Day 6
10. Bug #8: Preview Box Size (2 hours)

### Phase 4: Testing & QA - Day 7
- Integration testing
- Regression testing
- User acceptance testing

---

## Testing Strategy

### Unit Tests
- Ingredient consolidation logic
- JSON extraction methods
- Name sanitization
- Non-food detection

### Integration Tests
- End-to-end scanning flow
- Recipe generation with validation
- Meal plan management

### Manual Testing
- Camera permissions on physical device
- UI consistency across all views
- Edge cases for ingredient names
- Various recipe generation scenarios

---

## Success Metrics

- **Camera crash rate:** 0% (down from 100% on first camera access)
- **Ingredient duplication rate:** <2% (down from ~15%)
- **Non-food items in pantry:** 0% (down from occasional occurrences)
- **Recipe validation pass rate:** >95%
- **JSON parsing success rate:** >99%
- **Generic recipe names:** 0%
- **User satisfaction:** Target 4.5+/5.0

---

## Risks & Mitigation

| Risk | Impact | Mitigation |
|------|--------|------------|
| AI prompt changes affect other features | High | Comprehensive regression testing |
| Pluralization rules too aggressive | Medium | Whitelist for inherently plural items |
| JSON extraction breaks existing flows | High | Fallback to original method if new fails |
| Performance impact from validation | Low | Optimize validation logic, run async |

---

## Appendix

### Related Documentation
- V7 Tasks: Critical Bug Fixes PRD
- Ingredient Scanning Architecture
- Recipe Generation Service Documentation

### Code References
- `Application/Info.plist`
- `Features/1_ImageCapture/StagingViewModel.swift`
- `Features/RecipeGenerator/GeneratedRecipeDetailView.swift`
- `Features/Recipes/MealPlanCalendarView.swift`
- `Services/PantryService.swift`
- `Services/GeminiAPIService.swift`
- `Services/RecipeGenerationService.swift`
- `Utils/NameCanonicalizer.swift`
- `Models/RecipeDetail.swift`

---

**Document End**
