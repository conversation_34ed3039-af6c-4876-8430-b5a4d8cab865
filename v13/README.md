# V13: Critical Bug Fixes Implementation

## Document Control
- **Maintainer:** iOS Platform – Quality Assurance & Bug Fixes
- **Reviewers:** Feature Owners (Scanning, Recipe Generation, Pantry, UI/UX), QA Automation, Product Team
- **Version:** 13.0
- **Last Updated:** 2025-10-03
- **Status:** 📋 IMPLEMENTATION PLAN (Not Yet Implemented)
- **Review Status:** ✅ Comprehensive Bug Analysis Completed
- **Implementation Status:** ⚠️ PENDING - Code changes not yet applied
- **Priority:** P0 - Critical Stability & Data Quality Fixes
- **AI Model:** Gemini 2.5 Flash Lite (consistent with V12)

## ⚠️ IMPORTANT NOTICE

**This is an IMPLEMENTATION PLAN, not a completed implementation.**

The bugs described in this document have been identified and analyzed, and detailed technical solutions have been designed. However, **the actual code changes have NOT been implemented yet**. This documentation serves as:

1. **Bug Analysis Report** - Comprehensive analysis of 10 critical bugs
2. **Technical Design Document** - Detailed solutions with code examples
3. **Implementation Guide** - Step-by-step instructions for developers
4. **Quality Standards** - Code requirements and testing strategies

**Before using this as reference:** Verify the current implementation status by checking the codebase directly.

## Purpose
This README consolidates the documentation and engineering standards for implementing 10 critical bug fixes identified in V13 user testing. These fixes address app crashes, data quality issues, UI inconsistencies, and API integration defects that significantly impact user experience and app stability.

**Current State:** Bugs identified, solutions designed, implementation pending.

## Critical Requirements (For Implementation)
When implementing these fixes, the following requirements MUST be met:

1. **Stability First**: All P0 bugs (camera crash, data quality) MUST be fixed before any P1/P2 work begins.
2. **Backward Compatibility**: All changes MUST support existing user data without migration failures.
3. **Data Integrity**: Ingredient consolidation and categorization MUST be deterministic and reversible.
4. **API Resilience**: JSON parsing MUST handle all response formats (markdown, plain text, malformed).
5. **User Experience**: UI consistency MUST be maintained across all recipe views (Quick, Plans, Favorites).

## Implementation Status Tracking

| Bug # | Issue | Status | Code Files | Notes |
|-------|-------|--------|------------|-------|
| 1 | Camera Crash | ❌ Not Started | `Application/Info.plist`, `StagingViewModel.swift` | Missing `NSCameraUsageDescription` |
| 2 | Recipe Description | ❌ Not Started | `RecipeDetail.swift`, `GeneratedRecipeDetailView.swift` | Model lacks description field |
| 3 | UI Inconsistency | ⚠️ Partial | `MealPlanCalendarView.swift` | Basic list exists, needs card layout |
| 4 | Manage View | ⚠️ Partial | `ManageSelectionView.swift` | Functional but lacks summary/history |
| 5 | Ingredient Consolidation | ❌ Not Started | `NameCanonicalizer.swift` | No pluralization logic |
| 6 | Categorization | ❌ Not Started | `GeminiAPIService.swift`, `NonFoodDetector.swift` | NonFoodDetector doesn't exist |
| 7 | Recipe Ingredients | ❌ Not Started | `RecipeGenerationService.swift` | No validation logic |
| 8 | Preview Box | ❌ Not Started | `RecipeGeneratorView.swift` | Still uses lineLimit(1) |
| 9 | JSON Parsing | ❌ Not Started | `GeminiAPIService.swift`, `RecipeGenerationService.swift` | Simple string slicing only |
| 10 | Generic Names | ❌ Not Started | `RecipeServiceAdapter.swift` | Still generates "Pantry Idea 1" |

**Legend:**
- ✅ Complete - Fully implemented and tested
- ⚠️ Partial - Some work done, doesn't match documented plan
- ❌ Not Started - No implementation yet
- 🚧 In Progress - Currently being worked on

---

## 📚 Document Index

This README is the **single source of truth** for the V13 bug fixes implementation. All necessary information is contained in this directory.

### 🎯 Quick Navigation

**For Executives/Product Managers:**
- Jump to: [Quick Overview](#-quick-overview)
- Jump to: [Expected Outcomes](#-expected-outcomes)
- Jump to: [Deployment Strategy](#-deployment-strategy)

**For Engineers:**
- Jump to: [Technical Standards](#technical-standards--code-requirements)
- Jump to: [Implementation Phases](#-implementation-phases)
- Jump to: [Code Examples](#-code-examples)

**For Technical Leads:**
- Jump to: [Bug Analysis Summary](#-bug-analysis-summary)
- Jump to: [Testing Strategy](#-testing-strategy)
- Jump to: [Monitoring & Observability](#-monitoring--observability)

**Additional Documents:**
- 📄 **[v13_issues.md](./v13_issues.md)** - Original bug report from user testing
- 📖 **[v13_bug_fixes_prd.md](./v13_bug_fixes_prd.md)** - Detailed Product Requirements Document with technical solutions and code examples
- 📋 **[TASK_TRACKING.md](./TASK_TRACKING.md)** - Task assignments, progress tracking, and implementation checklists

---

## 🎯 Quick Overview

### Problem (Identified in User Testing)
User testing revealed 10 critical bugs affecting app stability, data quality, and user experience:
- **Crashes**: Camera access causes immediate app crash (Bug #1)
- **Data Quality**: Duplicate ingredients (Grape vs Grapes), non-food items in pantry (Bugs #5, #6)
- **UX Issues**: Missing recipe descriptions, inconsistent UI, generic dish names (Bugs #2, #3, #8, #10)
- **API Failures**: Intermittent JSON parsing errors (Bug #9)
- **Feature Gaps**: Non-functional Manage view, recipes with unavailable ingredients (Bugs #4, #7)

### Proposed Solution (Design Phase)
Systematic fixes across 4 layers:
1. **Platform Layer**: Add camera permissions, improve error handling
2. **Data Layer**: Implement pluralization rules, non-food filtering, strict validation
3. **API Layer**: Robust JSON parsing, enhanced prompts, ingredient validation
4. **UI Layer**: Consistent card layouts, description display, preview box sizing

### Expected Impact (After Implementation)
- 🎯 100% crash elimination (camera permissions)
- 🎯 95%+ data quality improvement (consolidation + categorization)
- 🎯 99%+ API reliability (robust JSON parsing)
- 🎯 Consistent UX across all features

### Estimated Timeline
- **Phase 1 (P0):** 4 days (Critical fixes - 6 bugs, 32 hours)
- **Phase 2 (P1):** 2.5 days (High priority - 3 bugs, 18 hours)
- **Phase 3 (P2):** 0.5 day (Polish - 1 bug, 2 hours)
- **Phase 4:** 1 day (Testing & QA)
- **Total:** 8 days development + deployment

**Note:** Timeline assumes full-time dedicated developer (8h/day). Adjust based on team capacity.

---

## 🚨 Bug Analysis Summary

### P0 - Critical (Must Fix Before Release)

| # | Issue | Current State | Impact | Est. Time |
|---|-------|---------------|--------|-----------|
| 1 | Camera Crash | ❌ Missing `NSCameraUsageDescription` | App crashes on camera access | 2h |
| 2 | Missing Recipe Description | ❌ Model lacks field | Poor UX in detail view | 4h |
| 5 | Ingredient Consolidation | ❌ No pluralization logic | Duplicate pantry items (Grape vs Grapes) | 8h |
| 6 | Incorrect Categorization | ❌ No NonFoodDetector | Non-food items in pantry | 6h |
| 7 | Unavailable Ingredients | ❌ No validation | Recipes can't be made | 6h |
| 9 | JSON Parsing Errors | ❌ Simple string slicing | Intermittent failures | 6h |

**Total P0 Time:** 32 hours (~4 days)
**P0 Implementation Status:** 0/6 complete (0%)

### P1 - High Priority

| # | Issue | Current State | Impact | Est. Time |
|---|-------|---------------|--------|-----------|
| 3 | UI Inconsistency | ⚠️ Basic list exists | Confusing user experience | 6h |
| 4 | Non-Functional Manage View | ⚠️ Partial (lacks features) | Missing summary/history | 8h |
| 10 | Generic Dish Names | ❌ Still "Pantry Idea 1" | Poor recipe appeal | 4h |

**Total P1 Time:** 18 hours (~2.5 days)
**P1 Implementation Status:** 0/3 complete (0%)

**Note on Bug #4:** `ManageSelectionView` exists and is functional. Enhancement approach adopted to add missing features (summary, history) rather than creating new view - this is lower risk, faster (8h vs 9+h), and avoids breaking changes to existing integrations.

### P2 - Medium Priority

| # | Issue | Current State | Impact | Est. Time |
|---|-------|---------------|--------|-----------|
| 8 | Small Preview Box | ❌ Still lineLimit(1) | Minor UX issue | 2h |

**Total P2 Time:** 2 hours
**P2 Implementation Status:** 0/1 complete (0%)

---

**Overall Implementation Status: 0/10 bugs fixed (0%)**

---

## 🎯 Quick Start

### For Developers

1. **Read the issues:** Start with `v13_issues.md` to understand user-reported problems
2. **Review solutions:** Read `v13_bug_fixes_prd.md` for detailed technical solutions (includes technical standards formerly tracked separately)
3. **Plan execution:** Use `TASK_TRACKING.md` for assignee ownership, task breakdown, and progress tracking
4. **Test thoroughly:** Each bug has specific acceptance criteria

### For Project Managers

1. **Timeline:** 6.5 days development + 1 day testing = ~8 days total
2. **Priority:** Focus on P0 bugs first (days 1-4)
3. **Resources:** 1 senior developer can complete all fixes
4. **Risk:** Low - all fixes are well-scoped and tested

---

## 📊 Bug Details at a Glance

### Bug #1: Camera Crash ⚠️
- **Root Cause:** Missing `NSCameraUsageDescription` in Info.plist
- **Fix:** Add permission description + availability check
- **Files:** `Info.plist`, `StagingViewModel.swift`

### Bug #2: Recipe Description Missing ⚠️
- **Root Cause:** `RecipeDetail` model lacks description field
- **Fix:** Add description field + display in UI
- **Files:** `RecipeDetail.swift`, `GeneratedRecipeDetailView.swift`, `GeminiAPIService.swift`

### Bug #3: UI Inconsistency ⚠️
- **Root Cause:** Plans use simple list, Quick uses cards
- **Fix:** Implement card layout for Plans
- **Files:** `MealPlanCalendarView.swift`

### Bug #4: Non-Functional Manage View ⚠️
- **Root Cause:** Existing `ManageSelectionView` lacks plan summary and history features
- **Fix:** Enhance existing view with summary, plan-level actions, history
- **Architecture Decision:** Enhance existing `ManageSelectionView` (not create new view) - lower risk, faster, no breaking changes
- **Files:** `ManageSelectionView.swift` (enhance), new `PlanHistoryView.swift`

### Bug #5: Ingredient Consolidation ⚠️
- **Root Cause:** No plural/singular normalization
- **Fix:** Add pluralization rules to canonicalizer
- **Files:** `NameCanonicalizer.swift`, `GeminiAPIService.swift`

### Bug #6: Incorrect Categorization ⚠️
- **Root Cause:** AI categorization too flexible, no non-food filtering
- **Fix:** Strengthen prompts + add non-food detector
- **Files:** `GeminiAPIService.swift`, new `NonFoodDetector.swift`

### Bug #7: Unavailable Ingredients ⚠️
- **Root Cause:** Prompt allows "staples" without definition
- **Fix:** Define staples explicitly + validate recipes
- **Files:** `RecipeGenerationService.swift`

### Bug #8: Small Preview Box ⚠️
- **Root Cause:** Tight line limits and fixed height
- **Fix:** Increase line limits + minimum height
- **Files:** `RecipeGeneratorView.swift`

### Bug #9: JSON Parsing Errors ⚠️
- **Root Cause:** Simple string matching fails on markdown/text
- **Fix:** Robust JSON extraction with regex
- **Files:** `GeminiAPIService.swift`, `RecipeGenerationService.swift`

### Bug #10: Generic Dish Names ⚠️
- **Root Cause:** Prompt doesn't emphasize creative naming
- **Fix:** Add naming rules + sanitization
- **Files:** `RecipeGenerationService.swift`

---

## Technical Standards & Code Requirements

### 1. Architecture Principles
1. **Data Flow Integrity**: Ingredient names MUST be canonicalized through `NameCanonicalizer` before storage.
2. **Graceful Degradation**: Missing optional fields (e.g., `RecipeDetail.description`) MUST NOT cause crashes.
3. **Actor Isolation**: All API services remain actors with proper concurrency handling.
4. **Sendable Compliance**: All data models crossing actor boundaries MUST conform to `Sendable`.
5. **Immutable Transformations**: All data transformations MUST be pure functions without side effects.

### 2. Code Quality Standards
1. **No Force Unwrapping**: Use `guard let`, `if let`, or optional chaining. Force unwraps prohibited.
2. **No Force Casts**: Use `as?` with nil handling. Force casts prohibited.
3. **Explicit Error Handling**: Separate user-facing messages from developer diagnostics.
4. **Logging Discipline**: Log all failures with context but redact sensitive information.
5. **Validation Gates**: Validate all AI outputs before persisting.

### 3. Prompt Engineering Standards
1. **Explicit Constraints**: Use "MUST", "CRITICAL" for hard requirements; "suggestions", "optional" for flexible guidance.
2. **Structured Output**: All prompts MUST specify exact JSON schema with validation rules.
3. **Singularization**: Explicitly instruct AI to use singular form (tomatoes → Tomato).
4. **Non-Food Filtering**: List specific non-food categories to reject.
5. **Token Efficiency**: Target <600 tokens for scanning, <800 for recipe generation.

### 4. Testing Requirements
1. **Unit Test Coverage**: Minimum 80% for new code, 90% for critical paths.
2. **Integration Tests**: Test complete user journeys with mocked APIs.
3. **Manual QA**: Test on physical devices (camera), various ingredient types, edge cases.
4. **Regression Tests**: Verify old data still works (backward compatibility).
5. **Performance Tests**: Monitor token usage, cache hit rate, API latency.

**Detailed technical standards are consolidated in:** [v13_bug_fixes_prd.md](./v13_bug_fixes_prd.md) ("Technical Standards" section)

---

## 📋 Implementation Phases

### Phase 1: Critical Fixes (Days 1-4)
**Priority:** P0 - Critical User Experience & Stability
**Estimated Time:** 32 hours (4 working days @ 8h/day)
**Risk:** 🟡 MEDIUM
**Dependencies:** None
**Owner:** TBD
**Reviewer:** Engineering Lead + QA

**Focus**: Stability and data quality

**Day 1 Tasks:**
- [ ] Bug #1: Camera Crash (2h)
  - Add `NSCameraUsageDescription` to Info.plist
  - Add camera availability check in StagingViewModel
  - Test on physical device
- [ ] Bug #5: Ingredient Consolidation (8h)
  - Implement pluralization rules in NameCanonicalizer
  - Add irregular plural handling
  - Update Gemini prompts for singular form
  - Test with common plurals (grapes, tomatoes, berries)

**Day 2 Tasks:**
- [ ] Bug #6: Categorization (6h)
  - Create NonFoodDetector utility
  - Enhance categorization prompts with explicit rules
  - Add non-food filtering in parseIngredientsResponse
  - Test with edge cases (dumplings, detergent, rice cakes)
- [ ] Bug #7: Recipe Ingredients (6h)
  - Define explicit list of allowed staples
  - Update prompt with strict ingredient rules
  - Add post-generation validation
  - Test recipes use only available ingredients

**Day 3 Tasks:**
- [ ] Bug #9: JSON Parsing (6h)
  - Create robust extractJSONArray method
  - Add regex patterns for markdown code blocks
  - Update parseIngredientsResponse and extractJSON
  - Test with various response formats
- [ ] Bug #2: Recipe Description (4h)
  - Add description field to RecipeDetail model
  - Update detail view to display description
  - Update Gemini prompt to request description
  - Test with new and existing recipes

**Success Criteria:**
- [ ] No camera crashes on any iOS device
- [ ] Ingredient duplication <2%
- [ ] No non-food items in pantry
- [ ] Recipes use only available ingredients
- [ ] JSON parsing >99% success rate
- [ ] Recipe descriptions visible in detail view

**Rollback Plan:**
- Camera: Revert Info.plist and StagingViewModel changes
- Consolidation: Disable pluralization via feature flag
- Categorization: Disable non-food filtering
- Recipe validation: Disable strict validation
- JSON parsing: Fallback to original method
- Description: Optional field, safe to ignore

**Documentation:** See [v13_bug_fixes_prd.md](./v13_bug_fixes_prd.md) for detailed technical solutions and [TASK_TRACKING.md](./TASK_TRACKING.md) for task checklists.

### Phase 2: High Priority (Days 5-6.5)
**Priority:** P1 - High User Experience Impact
**Estimated Time:** 18 hours (2.5 working days @ 8h/day)
**Risk:** 🟢 LOW
**Dependencies:** Phase 1 complete
**Owner:** TBD
**Reviewer:** Engineering Lead + QA + Product Manager

**Focus**: UX consistency and feature completeness

**Day 4 Tasks:**
- [ ] Bug #10: Dish Names (4h)
  - Add naming rules to recipe generation prompt
  - Implement sanitizeRecipeName method
  - Add regex patterns for generic names
  - Test with various ingredient combinations
- [ ] Bug #3: UI Consistency (6h)
  - Replace SlotRecipesListView simple list with card layout
  - Create RecipeCardView component matching Quick recipes
  - Add favorite and delete button functionality
  - Test visual consistency across all views

**Day 5 Tasks:**
- [ ] Bug #4: Manage View (8h) - **Enhancement Approach**
  - **Phase 1 (2h):** Add plan summary section to `ManageSelectionView`
    - Add statistics: total meals, unique days, unique recipes
    - Style with background and icons
  - **Phase 2 (2h):** Add plan-level actions
    - Extend `ManageAction` enum with `.deletePlan`
    - Implement delete entire plan functionality
    - Add confirmation alert
  - **Phase 3 (4h):** Add history view
    - Create `PlanHistoryView.swift` (new file)
    - Add History button to toolbar
    - Implement past plans display with swipe-to-delete
  - Test all enhancements and regression test existing functionality
  - **Rationale:** Enhancing existing view is lower risk, faster, and maintains backward compatibility

**Success Criteria:**
- [ ] No generic recipe names (e.g., "Idea 3")
- [ ] Consistent UI across Quick, Plans, Favorites
- [ ] Manage view fully functional with all buttons enabled

**Rollback Plan:**
- Dish names: Disable sanitization, use raw AI output
- UI consistency: Revert to simple list view
- Manage view: Hide Manage button until complete

**Documentation:** See [v13_bug_fixes_prd.md](./v13_bug_fixes_prd.md) for detailed technical solutions and [TASK_TRACKING.md](./TASK_TRACKING.md) for task checklists.

---

### Phase 3: Polish (Day 7)
**Priority:** P2 - Medium UX Improvement
**Estimated Time:** 2 hours (0.5 working day @ 8h/day)
**Risk:** 🟢 LOW
**Dependencies:** Phase 2 complete
**Owner:** TBD
**Reviewer:** Engineering Lead

**Focus**: Minor UX improvements

**Day 6 Tasks:**
- [ ] Bug #8: Preview Box (2h)
  - Update RecipeCard layout
  - Increase title line limit to 3
  - Increase description line limit to 4
  - Add minimum card height (140pt)
  - Test on different screen sizes
- [ ] Code cleanup and documentation (2h)
  - Update inline comments
  - Update API documentation
  - Clean up debug logs
  - Update version numbers

**Success Criteria:**
- [ ] Preview boxes show full content without clipping
- [ ] Code is well-documented
- [ ] No compiler warnings

**Rollback Plan:**
- Revert card layout changes
- No functional impact

**Documentation:** See [v13_bug_fixes_prd.md](./v13_bug_fixes_prd.md) for detailed technical solutions and [TASK_TRACKING.md](./TASK_TRACKING.md) for task checklists.

---

### Phase 4: Testing & QA (Day 8)
**Priority:** P0 - Critical Quality Gate
**Risk:** 🟢 LOW
**Dependencies:** All phases complete
**Owner:** QA Team
**Reviewer:** Engineering Lead + Product Manager

**Focus**: Comprehensive validation

**Testing Activities:**
- [ ] **Unit Tests** (2h)
  - Run full test suite
  - Verify >80% coverage for new code
  - Fix any failing tests
- [ ] **Integration Tests** (2h)
  - Test end-to-end flows
  - Verify API mocking works
  - Test data persistence
- [ ] **Manual QA** (3h)
  - Test on physical devices (camera)
  - Test with 20+ ingredient types
  - Test recipe generation edge cases
  - Test UI consistency across all views
  - Test backward compatibility with old data
- [ ] **Regression Testing** (1h)
  - Verify no new bugs introduced
  - Test all existing features
  - Check performance metrics

**Success Criteria:**
- [ ] All automated tests pass
- [ ] No critical bugs found
- [ ] Performance metrics acceptable
- [ ] Backward compatibility verified

**Documentation:** See [TASK_TRACKING.md](./TASK_TRACKING.md#testing-checklist) for execution checklists and test coverage tracking

---

## Code Review & Quality Gates

### Pre-Implementation Checklist
- [ ] All team members reviewed PRD and technical standards
- [ ] Feature flags configured for gradual rollout
- [ ] Analytics events defined and instrumentation ready
- [ ] Rollback procedures documented
- [ ] QA test plan reviewed and approved

### Code Review Requirements
All PRs for V13 bug fixes MUST:
1. **Reference Bug Number**: Title format: `[V13-#] Bug description` (e.g., `[V13-1] Fix camera crash`)
2. **Include Tests**: Minimum 80% coverage for new code
3. **Pass CI/CD**: All automated tests pass, no new warnings
4. **Documentation**: Update inline comments, README, and API docs
5. **Before/After**: Include screenshots or logs showing fix
6. **Backward Compatibility**: Verify old data still works
7. **Sign-Off**: Require approval from senior engineer + QA reviewer

### Merge Criteria
- [ ] All automated tests pass (unit, integration, UI)
- [ ] Code coverage meets threshold (80% new code, 90% critical paths)
- [ ] No new compiler warnings
- [ ] Static analysis passes (SwiftLint, no critical issues)
- [ ] Performance benchmarks meet targets
- [ ] Documentation updated
- [ ] QA sign-off obtained

### Post-Merge Validation
- [ ] Staging deployment successful
- [ ] Smoke tests pass
- [ ] Metrics dashboard shows expected behavior
- [ ] No error spikes in logs
- [ ] Performance within acceptable range

---

## ✅ Success Criteria

### Must Have (P0)
- ✅ No camera crashes
- ✅ Recipe descriptions visible
- ✅ No duplicate ingredients (grape/grapes)
- ✅ No non-food items in pantry
- ✅ Recipes use only available ingredients
- ✅ JSON parsing >99% success rate

### Should Have (P1)
- ✅ Consistent UI across all recipe views
- ✅ Functional meal plan management
- ✅ Descriptive recipe names (no "Idea 3")

### Nice to Have (P2)
- ✅ Larger preview boxes

---

## 📈 Success Metrics

### Technical Metrics
- Camera crash rate: 0% (from 100%)
- Ingredient duplication: <2% (from ~15%)
- Non-food items: 0% (from occasional)
- Recipe validation: >95% pass rate
- JSON parsing: >99% success rate
- Generic names: 0% (from ~10%)

### User Experience Metrics
- App stability: No crashes during normal use
- Data quality: Accurate categorization and consolidation
- Recipe quality: All recipes are makeable with pantry items
- UI consistency: Uniform experience across features

---

## ⚠️ Risk Mitigation

### Risk 1: Pluralization False Positives
**Impact:** Incorrect consolidation (e.g., "glass" → "glas")
**Probability:** MEDIUM
**Mitigation:** Whitelist for inherently plural items (noodles, oats, scissors)
**Rollback:** Disable pluralization via feature flag, revert to exact matching

### Risk 2: Non-Food Filter Too Aggressive
**Impact:** Legitimate food items filtered out
**Probability:** LOW
**Mitigation:** Conservative keyword list, manual review of filtered items in logs
**Rollback:** Disable non-food filtering via feature flag

### Risk 3: JSON Parsing Breaks Existing Flows
**Impact:** Recipe generation fails
**Probability:** LOW
**Mitigation:** Fallback to original parsing method if new method fails
**Rollback:** Revert to simple string matching

### Risk 4: UI Changes Break Accessibility
**Impact:** VoiceOver users can't navigate
**Probability:** LOW
**Mitigation:** Accessibility audit before deployment
**Rollback:** Revert UI changes to simple list view

### Risk 5: Backward Compatibility Issues
**Impact:** Old user data doesn't load
**Probability:** LOW
**Mitigation:** All new fields are optional, extensive testing with old data
**Rollback:** Feature flags allow disabling new features

---

## 🧪 Testing Strategy

### Unit Tests (Target: 80%+ coverage)

**Phase 1 Tests:**
- [ ] `NameCanonicalizerTests.swift`
  - Test singularization: grapes → Grape, tomatoes → Tomato
  - Test irregular plurals: children → child, people → person
  - Test edge cases: rice (uncountable), scissors (inherently plural)
  - Test case preservation: Grapes → Grape (not grape)
- [ ] `NonFoodDetectorTests.swift`
  - Test detergent, soap, shampoo → filtered
  - Test legitimate foods → not filtered
  - Test edge cases: "dish soap" vs "dish" (food)
- [ ] `JSONExtractionTests.swift`
  - Test plain JSON array
  - Test JSON in markdown code blocks
  - Test JSON with explanatory text
  - Test malformed JSON handling
- [ ] `RecipeDetailTests.swift`
  - Test Codable with description field
  - Test Codable without description (backward compatibility)

**Phase 2 Tests:**
- [ ] `RecipeNameSanitizationTests.swift`
  - Test generic pattern removal: "Pantry meal_lunch Idea 3" → cleaned
  - Test numbered names: "Recipe 1" → cleaned
  - Test valid names: "Garlic Butter Pasta" → unchanged
- [ ] `RecipeCardViewTests.swift`
  - Test card layout rendering
  - Test favorite button state
  - Test delete button action

**Phase 3 Tests:**
- [ ] `RecipeCardLayoutTests.swift`
  - Test minimum height enforcement
  - Test line limit behavior
  - Test responsive layout

### Integration Tests (End-to-End Flows)

- [ ] `IngredientScanningIntegrationTests.swift`
  - Test: Scan image → canonicalize → categorize → consolidate → display
  - Test: Scan "grapes" and "Grape" → single ingredient
  - Test: Scan detergent → filtered out
  - Use URLProtocol mocks for API calls
- [ ] `RecipeGenerationIntegrationTests.swift`
  - Test: Generate recipes → validate ingredients → display
  - Test: Recipes use only available ingredients
  - Test: Recipe names are descriptive (no "Idea 3")
  - Test: JSON parsing handles various formats
- [ ] `MealPlanManagementIntegrationTests.swift`
  - Test: Create plan → view → manage → delete
  - Test: UI consistency across Quick/Plans/Favorites
- [ ] `BackwardCompatibilityIntegrationTests.swift`
  - Test: Load old RecipeDetail (no description) → display gracefully
  - Test: Load old ingredients → consolidate with new rules
  - Test: Old saved recipes still work

### Performance Tests (Benchmarks)

- [ ] `IngredientProcessingPerformanceTests.swift`
  - Measure: Canonicalization time for 100 ingredients (target: <100ms)
  - Measure: Non-food detection time for 100 items (target: <50ms)
- [ ] `JSONParsingPerformanceTests.swift`
  - Measure: JSON extraction time for various formats (target: <10ms)
  - Measure: Success rate over 1000 samples (target: >99%)
- [ ] `RecipeGenerationPerformanceTests.swift`
  - Measure: API response time (target: <3s)
  - Measure: Token usage increase vs baseline (target: <10%)

### Manual QA Checklist

**Phase 1 Manual Tests:**
- [ ] Test camera on iPhone (iOS 15, 16, 17)
- [ ] Test permission grant/deny flows
- [ ] Scan 20+ different ingredients (produce, packaged, frozen, non-food)
- [ ] Verify "Grape" and "Grapes" consolidate
- [ ] Verify detergent is filtered out
- [ ] Generate recipes with 1, 5, 10, 20 ingredients
- [ ] Verify recipes use only available ingredients
- [ ] Test JSON parsing with various AI responses

**Phase 2 Manual Tests:**
- [ ] Verify no generic recipe names in 20+ generations
- [ ] Check UI consistency: Quick vs Plans vs Favorites
- [ ] Test Manage view: create, view, delete plans
- [ ] Test favorite button in all views
- [ ] Test delete button in all views

**Phase 3 Manual Tests:**
- [ ] Verify preview boxes show full content
- [ ] Test on iPhone SE (small screen)
- [ ] Test on iPhone Pro Max (large screen)
- [ ] Test on iPad

**Regression Tests:**
- [ ] Load app with old data (pre-V13)
- [ ] Verify existing recipes load
- [ ] Verify saved favorites intact
- [ ] Verify pantry items display correctly
- [ ] Test all existing features (scanning, generation, plans, favorites)

---

## 📊 Monitoring & Observability

### Key Metrics Dashboard

**Data Quality Metrics:**
- `ingredient.duplication_rate` (target: <2%)
- `ingredient.non_food_rate` (target: 0%)
- `ingredient.categorization_accuracy` (target: >95%)
- `ingredient.consolidation_success_rate` (target: >98%)

**API Reliability Metrics:**
- `api.json_parsing_success_rate` (target: >99%)
- `api.response_time_p50` (target: <2s)
- `api.response_time_p95` (target: <3s)
- `api.error_rate` (target: <0.1%)

**Recipe Quality Metrics:**
- `recipe.validation_pass_rate` (target: >95%)
- `recipe.generic_name_rate` (target: 0%)
- `recipe.description_present_rate` (target: 100%)
- `recipe.ingredient_availability_rate` (target: 100%)

**User Experience Metrics:**
- `app.crash_rate` (target: 0%)
- `camera.permission_grant_rate` (tracking)
- `ui.consistency_score` (manual review)
- `user.satisfaction_rating` (target: >4.5 stars)

### Logging Standards

**Structured Log Format:**
```swift
// Example log entry
{
  "timestamp": "2025-10-03T10:15:30Z",
  "level": "INFO",
  "service": "GeminiAPIService",
  "method": "canonicalizeIngredients",
  "input_count": 5,
  "output_count": 4,
  "filtered_count": 1,
  "filtered_items": ["laundry detergent"],
  "duration_ms": 1234,
  "status": "success"
}
```

**Log Levels:**
- **DEBUG**: Full prompts, responses, detailed diagnostics (DEBUG builds only)
- **INFO**: Request metadata (counts, timing, status)
- **WARNING**: Recoverable errors (fallback usage, validation failures, filtered items)
- **ERROR**: Unrecoverable errors (API failures, crashes, data corruption)

**Sensitive Data Handling:**
- ✅ Log: Counts, timing, status codes, filtered item names
- ❌ Redact: API keys, user IDs, personal information
- ❌ Redact: Full recipe content in production logs (log titles only)

### Alerting Rules

**Critical Alerts (Page on-call engineer):**
- Camera crash rate >0.1% for 5 minutes
- Ingredient duplication rate >10% for 15 minutes
- JSON parsing success rate <90% for 10 minutes
- API error rate >1% for 5 minutes

**Warning Alerts (Slack notification):**
- Ingredient duplication rate >5% for 30 minutes
- Non-food item rate >1% for 30 minutes
- Recipe validation pass rate <90% for 1 hour
- Generic recipe name rate >5% for 1 hour

**Info Alerts (Dashboard only):**
- Ingredient duplication rate 2-5%
- Recipe validation pass rate 90-95%
- JSON parsing success rate 95-99%

### Debug Tools

**Feature Flags:**
- `v13_ingredient_pluralization_enabled` (default: true after rollout)
- `v13_non_food_filtering_enabled` (default: true after rollout)
- `v13_strict_recipe_validation_enabled` (default: true after rollout)
- `v13_robust_json_parsing_enabled` (default: true after rollout)
- `v13_verbose_logging_enabled` (default: false, enable for debugging)

**Debug Menu (DEBUG builds only):**
- View last ingredient scan results (before/after canonicalization)
- View last recipe generation (prompt + response)
- View filtered non-food items
- Force clear all caches
- Toggle between old/new implementations
- View metrics for current session

---

## 🚀 Deployment Strategy

### Pre-Deployment
1. Complete all P0 and P1 fixes
2. Pass all automated tests
3. Complete manual testing
4. Code review approval
5. Update documentation

### Deployment
1. Create release branch
2. Submit to TestFlight
3. Internal testing (2-3 days)
4. Fix critical issues if any
5. Submit to App Store

### Post-Deployment
1. Monitor crash reports
2. Track success metrics
3. Gather user feedback
4. Plan next iteration

---

## 📞 Support

### Questions?
- **Technical:** Review `v13_bug_fixes_prd.md` for detailed solutions and standards
- **Implementation:** Coordinate via `TASK_TRACKING.md`
- **Testing:** Reference acceptance criteria in each bug section and the test checklist in `TASK_TRACKING.md`

### Issues During Implementation?
1. Check the PRD for detailed technical solutions
2. Review related code sections
3. Test incrementally
4. Document any deviations

---

## 📋 Code Examples

### Example 1: NameCanonicalizer with Pluralization

```swift
// Utils/NameCanonicalizer.swift
struct NameCanonicalizer {
    private let stopwords: Set<String> = ["and","or","of","the","a","an","in"]

    // NEW: Pluralization rules
    private let pluralRules: [(suffix: String, replacement: String)] = [
        ("ies", "y"),      // berries -> berry
        ("ves", "f"),      // loaves -> loaf
        ("oes", "o"),      // tomatoes -> tomato
        ("ses", "s"),      // glasses -> glass
        ("xes", "x"),      // boxes -> box
        ("ches", "ch"),    // peaches -> peach
        ("shes", "sh"),    // dishes -> dish
        ("s", "")          // grapes -> grape (fallback)
    ]

    // NEW: Irregular plurals
    private let irregulars: [String: String] = [
        "children": "child",
        "people": "person",
        "men": "man",
        "women": "woman",
        "teeth": "tooth",
        "feet": "foot",
        "mice": "mouse",
        "geese": "goose"
    ]

    func canonicalize(_ input: String, locale: Locale = .current) -> String {
        // ... existing NFC normalization, punctuation handling

        let singularized = singularizeTokens(titled)  // NEW

        // ... rest of processing
    }

    // NEW: Singularize tokens
    private func singularizeTokens(_ tokens: [Token]) -> [Token] {
        return tokens.map { token in
            switch token {
            case .word(let w):
                return .word(singularize(w))
            case .comma:
                return .comma
            }
        }
    }

    // NEW: Convert plural to singular
    private func singularize(_ word: String) -> String {
        let lower = word.lowercased()
        guard lower.count > 3 else { return word }

        // Check irregulars first
        if let singular = irregulars[lower] {
            return preserveCase(original: word, template: singular)
        }

        // Apply suffix rules
        for rule in pluralRules {
            if lower.hasSuffix(rule.suffix) {
                let base = String(lower.dropLast(rule.suffix.count))
                let singular = base + rule.replacement
                return preserveCase(original: word, template: singular)
            }
        }

        return word
    }

    // NEW: Preserve original capitalization
    private func preserveCase(original: String, template: String) -> String {
        if original.first?.isUppercase == true {
            return template.prefix(1).uppercased() + template.dropFirst()
        }
        return template
    }
}
```

### Example 2: NonFoodDetector

```swift
// Utils/NonFoodDetector.swift
struct NonFoodDetector {
    private let nonFoodKeywords: Set<String> = [
        // Cleaning products
        "detergent", "soap", "cleaner", "bleach", "disinfectant", "wipes",
        // Personal care
        "shampoo", "conditioner", "toothpaste", "deodorant", "lotion",
        // Household items
        "paper towel", "tissue", "napkin", "foil", "wrap", "bag",
        // Medications
        "medicine", "pill", "tablet", "vitamin", "supplement",
        // Other
        "battery", "light bulb", "pen", "pencil"
    ]

    func isNonFood(_ name: String) -> Bool {
        let lower = name.lowercased()
        return nonFoodKeywords.contains(where: { lower.contains($0) })
    }
}
```

### Example 3: Robust JSON Extraction

```swift
// Services/GeminiAPIService.swift
actor GeminiAPIService {

    /// Robust JSON extraction from AI response
    private func extractJSONArray(from response: String) -> Data? {
        // Try direct parsing first
        if let data = response.data(using: .utf8),
           let _ = try? JSONSerialization.jsonObject(with: data) as? [[String: Any]] {
            return data
        }

        // Try extracting from markdown code blocks
        let patterns = [
            "```json\\s*\\n(.+?)\\n```",  // ```json ... ```
            "```\\s*\\n(.+?)\\n```",      // ``` ... ```
            "\\[\\s*\\{.+?\\}\\s*\\]"     // [ { ... } ]
        ]

        for pattern in patterns {
            if let regex = try? NSRegularExpression(pattern: pattern, options: [.dotMatchesLineSeparators]),
               let match = regex.firstMatch(in: response, range: NSRange(response.startIndex..., in: response)) {

                let matchRange = match.range(at: match.numberOfRanges > 1 ? 1 : 0)
                if let swiftRange = Range(matchRange, in: response) {
                    let extracted = String(response[swiftRange])
                    if let data = extracted.data(using: .utf8),
                       let _ = try? JSONSerialization.jsonObject(with: data) as? [[String: Any]] {
                        return data
                    }
                }
            }
        }

        // Fallback: find first [ to last ]
        guard let startIndex = response.firstIndex(of: "["),
              let endIndex = response.lastIndex(of: "]") else {
            print("❌ No JSON array found in response")
            return nil
        }

        let jsonString = String(response[startIndex...endIndex])
        return jsonString.data(using: .utf8)
    }
}
```

### Example 4: Enhanced Categorization Prompt

```swift
// Services/GeminiAPIService.swift
private func buildCanonicalizePrompt(visionOutputs: [VisionOutput], allowedCategories: [String]) -> String {
    var prompt = """
    You are a food ingredient analyzer. Process the following OCR text and labels from images.

    CRITICAL RULES:
    1. ONLY process food items. REJECT these non-food categories:
       - Cleaning products (detergent, soap, bleach, etc.)
       - Personal care (shampoo, toothpaste, etc.)
       - Household items (paper towels, batteries, etc.)
       - Medications and supplements

    2. For each FOOD item:
       - Extract ingredient name, remove brands/sizes/quantities
       - Normalize to SINGULAR form (tomatoes → Tomato, grapes → Grape)
       - Categorize into EXACTLY ONE: \(allowedCategories.joined(separator: ", "))

    3. Categorization guidelines:
       - Dumplings, wontons, buns → Frozen Foods (if frozen) or Grains, Pasta & Legumes (if fresh)
       - Rice cakes, mochi → Grains, Pasta & Legumes
       - Prepared meals → Frozen Foods or Canned & Broths (based on packaging)
       - When uncertain, prefer specific category over "Others"

    4. If item is clearly non-food, OMIT it entirely from output

    Return ONLY JSON array: [{"name":"ingredient name","category":"exact category"}]
    """

    for (index, output) in visionOutputs.enumerated() {
        prompt += "\n\nIMAGE \(index + 1):\nOCR TEXT: \(output.ocrText)\nLABELS: \(output.labels.joined(separator: ", "))"
    }

    return prompt
}
```

### Example 5: Recipe Name Sanitization

```swift
// Services/RecipeGenerationService.swift
private func sanitizeRecipeName(_ name: String) -> String {
    var sanitized = name.trimmingCharacters(in: .whitespacesAndNewlines)

    // Remove common generic patterns
    let genericPatterns = [
        "Pantry meal_",
        "Pantry recipe",
        "Meal \\d+",
        "Recipe \\d+",
        "Idea \\d+",
        "Option \\d+"
    ]

    for pattern in genericPatterns {
        if let regex = try? NSRegularExpression(pattern: pattern, options: .caseInsensitive) {
            sanitized = regex.stringByReplacingMatches(
                in: sanitized,
                range: NSRange(sanitized.startIndex..., in: sanitized),
                withTemplate: ""
            ).trimmingCharacters(in: .whitespacesAndNewlines)
        }
    }

    // If name is now empty or too short, generate fallback
    if sanitized.count < 3 {
        sanitized = "Delicious Homemade Dish"
    }

    return sanitized
}
```

---

## 📝 Version History

| Version | Date | Author | Changes |
|---------|------|--------|---------|
| 13.0 | 2025-10-03 | QA Team | Initial release with 10 critical bug fixes |
| 13.1 | TBD | TBD | Post-deployment optimizations |

---

## 📝 Change Log

### 2025-10-03
- Initial documentation created
- All 10 bugs analyzed and documented
- Implementation plan finalized
- Ready for development

---

## 🔗 Related Documentation

- **V7 Tasks:** Previous bug fix iteration
- **Ingredient Scanning Architecture:** System design docs
- **Recipe Generation Service:** API documentation
- **Testing Guidelines:** QA procedures

---

**Status:** ✅ Ready for Implementation  
**Next Steps:** Begin Phase 1 (P0 Critical Fixes)  
**Estimated Completion:** 8 days from start
