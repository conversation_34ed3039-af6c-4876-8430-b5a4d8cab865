# All Gemini API Prompts

This document contains all prompts used for Gemini API calls in the Ingredient Scanner project, organized by functionality.

**Last Updated:** 2025-10-02  
**Branch:** hao2.5lite

---

## Table of Contents

1. [Recipe Idea Generation](#1-recipe-idea-generation)
2. [Recipe Detail Generation (Current)](#2-recipe-detail-generation-current)
3. [Recipe Detail Generation (Legacy)](#3-recipe-detail-generation-legacy)
4. [Ingredient Canonicalization (Vision/OCR)](#4-ingredient-canonicalization-visionocr)
5. [Ingredient Canonicalization (Custom Input)](#5-ingredient-canonicalization-custom-input)

---

## 1. Recipe Idea Generation

**Purpose:** Generate multiple recipe ideas based on available ingredients and user preferences  
**Location:** `Services/RecipeGenerationService.swift` → `createRecipePrompt(from:preferences:)`  
**Status:** ✅ Active (Production)  
**Called by:** `RecipeGenerationService.generateMealIdeas()` → `RecipeServiceAdapter` → `RecipeGeneratorViewModel`

### Prompt Template:

```
Generate {dishTarget} recipes. Ingredients: {ingredients}. Servings: {servings}. Time: ~{cookingTimeMinutes}min.

Constraints:
- Return at least {dishTarget} distinct recipes; we will trim to {dishTarget} if extras are provided.
- All recipes must be suitable for {mealType} meals.
- Cuisine suggestions (not strict requirements): {cuisines}. You may use one, mix multiple, create fusion dishes, or draw inspiration from these styles.
- Dietary restrictions: {dietaryRestrictions}
- Allergies/intolerances: {allergiesAndIntolerances}
- Strict exclusions: {strictExclusions}
- Additional request: {additionalRequest}
- Special equipment available (optional to use): {equipmentOwned}. You may also use basic kitchen equipment (microwave, oven, stovetop).
- [If no equipment selected] Assume basic kitchen equipment is available (microwave, oven, stovetop).
- [If numberOfKids > 0] Family includes {numberOfKids} kid(s) - make recipes kid-friendly with milder spices, familiar flavors, and simpler textures.

JSON array format:
[{"title":"Recipe Name","description":"Brief","ingredients":["item1","item2"],"instructions":["step1","step2"],"cookingTime":"30 minutes","servings":{servings},"difficulty":"easy|medium|hard","nutrition":{"calories":"350","protein":"25g","carbs":"30g","fat":"15g"}}]

Rules: Use provided ingredients + staples. Avoid allergens. Brief instructions (3-5 steps). No measurements in ingredients.
```

### Parameters:
- `{dishTarget}`: Number of recipes to generate (1-12)
- `{ingredients}`: Comma-separated list of available ingredients
- `{servings}`: Target number of servings
- `{cookingTimeMinutes}`: Target cooking time in minutes
- `{mealType}`: breakfast, lunch, dinner, or snack
- `{cuisines}`: Comma-separated list of cuisine preferences (optional, flexible)
- `{dietaryRestrictions}`: User's dietary restrictions
- `{allergiesAndIntolerances}`: User's allergies and intolerances
- `{strictExclusions}`: Items user refuses to eat
- `{additionalRequest}`: Free-form user request
- `{equipmentOwned}`: Available kitchen equipment (optional)
- `{numberOfKids}`: Number of children in family (triggers kid-friendly mode)

### Key Features:
- ✅ Cuisines treated as **suggestions** (fusion allowed, not all must appear)
- ✅ Equipment treated as **optional enhancement** (not exclusive requirement)
- ✅ Kid-friendly adjustments when children are present
- ✅ Brief instructions (3-5 steps) without measurements
- ✅ Ingredients listed without measurements (expanded later in detail generation)

---

## 2. Recipe Detail Generation (Current)

**Purpose:** Expand a base recipe idea into detailed instructions with measurements  
**Location:** `Services/GeminiAPIService.swift` → `buildRecipeDetailPrompt(baseRecipe:pantryContext:equipmentOwned:)`  
**Status:** ✅ Active (Production) - V12 Implementation  
**Called by:** `RecipeServiceAdapter.prefetchRecipeDetails()` and `GeneratedRecipeDetailView.fetchRecipeDetail()`

### Prompt Template:

```
Expand recipe: "{recipeTitle}". Servings: {servings}. Time: {cookingTime}. Difficulty: {difficulty}. Ingredients: {ingredients}.

MUST MAINTAIN: servings={servings}, difficulty={difficulty}, time≈{cookingTime} (±10%), all ingredients.

Task: Add measurements to ingredients. Generate 6-12 specific steps with temps/times.
[If pantryContext provided] Pantry: {pantryContext}.
[If equipmentOwned provided] Equipment: {equipmentOwned}.

JSON format:
{"title":"{recipeTitle}","servings":{servings},"totalTimeMinutes":<number>,"difficulty":"{difficulty}","ingredients":["2 cups flour","1 tsp salt"],"steps":["Step 1","Step 2"],"nutrition":{"calories":320,"protein":"12g","carbs":"45g","fat":"8g"}}
```

### Parameters:
- `{recipeTitle}`: Title from base recipe (must be maintained exactly)
- `{servings}`: Number of servings from base recipe (must be maintained)
- `{cookingTime}`: Cooking time from base recipe (must stay within ±10%)
- `{difficulty}`: Difficulty level from base recipe (must be maintained)
- `{ingredients}`: Comma-separated list of ingredients from base recipe
- `{pantryContext}`: Optional pantry context for ingredient suggestions
- `{equipmentOwned}`: Optional equipment list for method refinement

### Key Features:
- ✅ **Grounded on base recipe** - expands existing recipe instead of regenerating
- ✅ Maintains consistency (servings, difficulty, time, ingredients)
- ✅ Adds specific measurements to ingredients
- ✅ Generates 6-12 detailed, actionable steps
- ✅ Compact prompt for faster API response

---

## 3. Recipe Detail Generation (Legacy)

**Purpose:** Generate recipe details from title only (without base recipe)  
**Location:** `Services/GeminiAPIService.swift` → `buildRecipeDetailPrompt(title:pantryContext:cuisines:equipmentOwned:)`  
**Status:** ⚠️ Legacy (Kept for backward compatibility)  
**Called by:** Fallback path only

### Prompt Template:

```
You are a professional chef and recipe developer. Generate a detailed recipe for: "{title}"

Requirements:
1. Create a complete, actionable recipe with specific ingredients and step-by-step instructions
2. Include precise measurements, cooking times, and temperatures where applicable
3. Provide 6-12 detailed cooking steps that are clear and specific
4. Avoid generic instructions like "cook as preferred" or "season to taste"
5. Include realistic cooking and prep times

[If pantryContext provided]
Available pantry context:
{pantryContext}

[If cuisines provided]
Preferred cuisines: {cuisines}
Consider these cuisine styles when developing the recipe.

[If equipmentOwned provided]
Available equipment: {equipmentOwned}
Tailor cooking methods to use available equipment.

Return the response as a JSON object with this exact structure:
{
  "title": "Recipe Name",
  "servings": 4,
  "totalTimeMinutes": 45,
  "difficulty": "medium",
  "ingredients": [
    "2 cups all-purpose flour",
    "1 tsp salt",
    "3 large eggs"
  ],
  "steps": [
    "Preheat oven to 375°F (190°C)",
    "In a large bowl, combine flour and salt",
    "Beat eggs in a separate bowl"
  ],
  "nutrition": {
    "calories": "320",
    "protein": "12g",
    "carbs": "45g",
    "fat": "8g"
  }
}

Ensure:
- ingredients array has 5-15 items with specific measurements
- steps array has 6-12 detailed, actionable instructions
- difficulty is one of: "easy", "medium", "hard"
- totalTimeMinutes includes prep + cooking time
- nutrition values are realistic estimates
```

### Parameters:
- `{title}`: Recipe title
- `{pantryContext}`: Optional pantry context
- `{cuisines}`: Comma-separated list of cuisine preferences
- `{equipmentOwned}`: Comma-separated list of available equipment

### Key Features:
- ⚠️ Regenerates from scratch (no base recipe grounding)
- ⚠️ Can produce inconsistent results vs. recipe ideas
- ✅ More detailed prompt with explicit requirements
- ✅ Kept for backward compatibility

---

## 4. Ingredient Canonicalization (Vision/OCR)

**Purpose:** Process OCR text and image labels to extract and categorize ingredients  
**Location:** `Services/GeminiAPIService.swift` → `buildCanonicalizePrompt(visionOutputs:allowedCategories:)`  
**Status:** ✅ Active (Production)  
**Called by:** `GeminiAPIService.canonicalizeIngredients()`

### Prompt Template:

```
You are a food ingredient analyzer. Process the following OCR text and labels from images of food items, receipts, or packaging.

Tasks:
- Extract ingredient names from OCR text and labels
- Clean up names: remove brands, sizes, quantities, and marketing text
- Preserve meaningful descriptors like "whole milk", "unsalted butter", "brown rice"
- Capitalize the first letter of each ingredient name (e.g., "whole milk" -> "Whole milk")
- Assign EXACTLY ONE category per item. The category MUST be one of: {allowedCategories}
- Exclude non-food items, duplicates, or gibberish entirely
- Normalize simple pluralization/singularization (e.g., "tomatoes" -> "tomato" if appropriate)

Return ONLY a JSON array of objects with fields name and category. Example:
[{"name":"Whole milk","category":"Dairy"},{"name":"Tomato","category":"Vegetables"}]

IMAGE 1:
OCR TEXT: {ocrText1}
LABELS: {labels1}

IMAGE 2:
OCR TEXT: {ocrText2}
LABELS: {labels2}

[... additional images ...]
```

### Parameters:
- `{allowedCategories}`: Comma-separated list of valid category names
- `{ocrTextN}`: OCR text extracted from image N
- `{labelsN}`: Comma-separated labels detected in image N

### Key Features:
- ✅ Processes multiple images in one API call
- ✅ Combines OCR text + vision labels for better accuracy
- ✅ Strict category validation
- ✅ Removes duplicates and non-food items
- ✅ Normalizes ingredient names

---

## 5. Ingredient Canonicalization (Custom Input)

**Purpose:** Clean and categorize free-form ingredient names typed by user  
**Location:** `Services/GeminiAPIService.swift` → `canonicalizeCustomIngredients()`  
**Status:** ✅ Active (Production)  
**Called by:** Add ingredient sheet / manual entry flow

### Prompt Template:

```
You are an assistant that standardizes a SHORT list of free-form pantry ingredients typed by a user.

Tasks:
- For each line item below, output a cleaned ingredient name: remove brands, sizes, quantities, and marketing text.
- Preserve meaningful descriptors like "whole milk", "unsalted butter", "brown rice".
- Capitalize the first letter of each ingredient name (e.g., "whole milk" -> "Whole milk").
- Assign EXACTLY ONE category per item. The category MUST be one of: {allowedCategories}
- Exclude non-food or gibberish items entirely.
- Normalize simple pluralization/singularization (e.g., "tomatoes" -> "tomato" if appropriate).

Return ONLY a JSON array of objects with fields name and category. Example:
[{"name":"Whole milk","category":"Dairy"}]

INPUT ITEMS:
- {customInput1}
- {customInput2}
- {customInput3}
[... additional items ...]
```

### Parameters:
- `{allowedCategories}`: Comma-separated list of valid category names
- `{customInputN}`: Free-form ingredient name typed by user

### Key Features:
- ✅ Handles free-form user input
- ✅ Removes brand names and quantities
- ✅ Strict category validation
- ✅ Excludes non-food items
- ✅ Normalizes ingredient names

---

## API Configuration

All prompts use the following Gemini API configuration:

```json
{
  "generationConfig": {
    "temperature": 0.7,
    "topP": 0.95,
    "maxOutputTokens": <varies by prompt>,
    "response_mime_type": "application/json"
  }
}
```

### Token Limits by Prompt Type:
- **Recipe Ideas:** `dishCount × 200 + 100` tokens (dynamic based on dish count)
- **Recipe Detail (Current):** 1024 tokens
- **Recipe Detail (Legacy):** 1024 tokens
- **Ingredient Canonicalization:** 256 tokens

---

## Notes

1. **All prompts enforce JSON output** via `response_mime_type: "application/json"`
2. **Cuisines are suggestions** - fusion and creative interpretation allowed
3. **Equipment is optional** - basic equipment (microwave, oven, stovetop) always assumed
4. **Kid-friendly mode** - triggered when `numberOfKids > 0` in user profile
5. **Recipe detail grounding** - V12 implementation uses base recipe for consistency

