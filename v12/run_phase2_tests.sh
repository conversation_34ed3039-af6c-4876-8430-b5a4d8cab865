#!/bin/bash

# Phase 2 Testing Script
# Runs all Phase 2 tests and generates a report

set -e  # Exit on error

echo "🧪 Phase 2 Testing Suite"
echo "========================"
echo ""
echo "This script will run all Phase 2 tests for Recipe Detail Grounding"
echo ""

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_DIR="/Users/<USER>/Desktop/ingredient-scanner"
TEST_FILE="$PROJECT_DIR/v12/Phase2Tests.swift"
SCHEME="IngredientScanner"
DESTINATION="platform=iOS Simulator,name=iPhone 16"

# Check if test file exists
if [ ! -f "$TEST_FILE" ]; then
    echo -e "${RED}❌ Error: Test file not found at $TEST_FILE${NC}"
    exit 1
fi

echo -e "${BLUE}📋 Test Configuration:${NC}"
echo "   Project: $PROJECT_DIR"
echo "   Test File: Phase2Tests.swift"
echo "   Scheme: $SCHEME"
echo "   Destination: $DESTINATION"
echo ""

# Check if we need to copy the test file to the Tests directory
TESTS_DIR="$PROJECT_DIR/Tests"
if [ ! -d "$TESTS_DIR" ]; then
    echo -e "${YELLOW}⚠️  Tests directory not found, creating...${NC}"
    mkdir -p "$TESTS_DIR"
fi

# Copy test file to Tests directory if not already there
if [ ! -f "$TESTS_DIR/Phase2Tests.swift" ]; then
    echo -e "${BLUE}📦 Copying test file to Tests directory...${NC}"
    cp "$TEST_FILE" "$TESTS_DIR/"
    echo -e "${GREEN}✓ Test file copied${NC}"
fi

echo ""
echo -e "${BLUE}🔨 Building project...${NC}"
xcodebuild -scheme "$SCHEME" -destination "$DESTINATION" build 2>&1 | grep -E "Build Succeeded|BUILD FAILED|error:" || true

if [ ${PIPESTATUS[0]} -ne 0 ]; then
    echo -e "${RED}❌ Build failed${NC}"
    exit 1
fi

echo -e "${GREEN}✓ Build succeeded${NC}"
echo ""

echo -e "${BLUE}🧪 Running Phase 2 Tests...${NC}"
echo ""

# Run tests and capture output
TEST_OUTPUT=$(xcodebuild test \
    -scheme "$SCHEME" \
    -destination "$DESTINATION" \
    -only-testing:IngredientScannerTests/Phase2Tests 2>&1)

TEST_EXIT_CODE=$?

# Display test output
echo "$TEST_OUTPUT" | grep -E "Test Case|Test Suite|passed|failed|✅|❌|📝|📊" || true

echo ""
echo "================================"
echo ""

# Check test results
if [ $TEST_EXIT_CODE -eq 0 ]; then
    echo -e "${GREEN}✅ All Phase 2 Tests Passed!${NC}"
    echo ""
    echo "Test Summary:"
    echo "$TEST_OUTPUT" | grep -E "Test Suite.*passed" || true
    exit 0
else
    echo -e "${RED}❌ Some Phase 2 Tests Failed${NC}"
    echo ""
    echo "Failed Tests:"
    echo "$TEST_OUTPUT" | grep -E "failed|error:" || true
    exit 1
fi

