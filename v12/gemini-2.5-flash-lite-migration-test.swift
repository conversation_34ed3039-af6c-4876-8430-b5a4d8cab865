import Foundation

/// Gemini 2.5 Flash Lite Migration Test
/// This test verifies that the migration from Gemini 2.5 Flash to 2.5 Flash Lite is successful
/// and that all API calls work correctly with the new model.
///
/// Test Date: 2025-10-01
/// Migration: gemini-2.5-flash → gemini-2.5-flash-lite
/// Reference: https://ai.google.dev/gemini-api/docs/models/gemini#gemini-2.5-flash-lite

struct GeminiFlashLiteMigrationTest {
    
    // MARK: - Configuration
    
    private let apiKey: String
    private let baseURL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent"
    
    init(apiKey: String) {
        self.apiKey = apiKey
    }
    
    // MARK: - Test Cases
    
    /// Test 1: Basic text generation
    func testBasicTextGeneration() async throws {
        print("🧪 Test 1: Basic Text Generation")
        
        let prompt = "Generate a simple recipe idea using chicken and rice. Return JSON format: {\"title\":\"...\",\"description\":\"...\"}"
        
        let response = try await callGeminiAPI(prompt: prompt, maxTokens: 500)
        
        guard response.contains("title") && response.contains("description") else {
            throw TestError.invalidResponse("Response missing required fields")
        }
        
        print("✅ Test 1 PASSED: Basic text generation works")
        print("   Response length: \(response.count) chars")
    }
    
    /// Test 2: JSON structured output
    func testStructuredOutput() async throws {
        print("\n🧪 Test 2: Structured JSON Output")
        
        let prompt = """
        Generate 2 recipe ideas. Return JSON array:
        [{"title":"Recipe 1","ingredients":["item1","item2"],"instructions":["step1","step2"]}]
        """
        
        let response = try await callGeminiAPI(prompt: prompt, maxTokens: 1024)
        
        // Try to parse as JSON
        guard let jsonData = response.data(using: .utf8),
              let _ = try? JSONSerialization.jsonObject(with: jsonData) else {
            throw TestError.invalidResponse("Response is not valid JSON")
        }
        
        print("✅ Test 2 PASSED: Structured JSON output works")
        print("   Response is valid JSON")
    }
    
    /// Test 3: Performance test (measure response time)
    func testPerformance() async throws {
        print("\n🧪 Test 3: Performance Test")
        
        let prompt = "Generate 1 simple recipe idea. Return JSON: {\"title\":\"...\",\"description\":\"...\"}"
        
        let startTime = Date()
        let response = try await callGeminiAPI(prompt: prompt, maxTokens: 500)
        let duration = Date().timeIntervalSince(startTime)
        
        guard response.contains("title") else {
            throw TestError.invalidResponse("Response missing title")
        }
        
        print("✅ Test 3 PASSED: Performance test completed")
        print("   Response time: \(String(format: "%.2f", duration))s")
        print("   Expected: < 10s (Flash Lite should be faster than Flash)")
        
        if duration > 10 {
            print("   ⚠️  WARNING: Response time is slower than expected")
        }
    }
    
    /// Test 4: Token limits test
    func testTokenLimits() async throws {
        print("\n🧪 Test 4: Token Limits Test")
        
        let prompt = "Generate 5 detailed recipe ideas with ingredients and instructions."
        
        let response = try await callGeminiAPI(prompt: prompt, maxTokens: 2048)
        
        guard response.count > 100 else {
            throw TestError.invalidResponse("Response too short")
        }
        
        print("✅ Test 4 PASSED: Token limits work correctly")
        print("   Response length: \(response.count) chars")
    }
    
    /// Test 5: Error handling test
    func testErrorHandling() async throws {
        print("\n🧪 Test 5: Error Handling Test")
        
        // Test with empty prompt
        do {
            _ = try await callGeminiAPI(prompt: "", maxTokens: 100)
            throw TestError.unexpectedSuccess("Empty prompt should fail")
        } catch {
            print("✅ Test 5 PASSED: Error handling works correctly")
            print("   Error: \(error)")
        }
    }
    
    // MARK: - Helper Methods
    
    private func callGeminiAPI(prompt: String, maxTokens: Int) async throws -> String {
        guard !apiKey.isEmpty && apiKey != "YOUR_GEMINI_API_KEY_HERE" else {
            throw TestError.apiKeyNotConfigured
        }
        
        let url = URL(string: "\(baseURL)?key=\(apiKey)")!
        
        let requestBody: [String: Any] = [
            "contents": [
                [
                    "parts": [
                        ["text": prompt]
                    ]
                ]
            ],
            "generationConfig": [
                "temperature": 0.7,
                "topP": 0.95,
                "maxOutputTokens": maxTokens,
                "response_mime_type": "application/json"
            ]
        ]
        
        let jsonData = try JSONSerialization.data(withJSONObject: requestBody)
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.httpBody = jsonData
        request.timeoutInterval = 60.0
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw TestError.invalidResponse("Invalid HTTP response")
        }
        
        guard httpResponse.statusCode == 200 else {
            let errorBody = String(data: data, encoding: .utf8) ?? "No error body"
            throw TestError.apiError("HTTP \(httpResponse.statusCode): \(errorBody)")
        }
        
        // Parse response
        guard let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
              let candidates = json["candidates"] as? [[String: Any]],
              let firstCandidate = candidates.first,
              let content = firstCandidate["content"] as? [String: Any],
              let parts = content["parts"] as? [[String: Any]],
              let firstPart = parts.first,
              let text = firstPart["text"] as? String else {
            throw TestError.parsingError("Failed to parse response")
        }
        
        return text
    }
    
    // MARK: - Test Runner
    
    func runAllTests() async {
        print("🚀 Starting Gemini 2.5 Flash Lite Migration Tests")
        print("=" + String(repeating: "=", count: 60))
        print("Model: gemini-2.5-flash-lite")
        print("Endpoint: \(baseURL)")
        print("=" + String(repeating: "=", count: 60))
        
        var passedTests = 0
        var failedTests = 0
        
        // Test 1
        do {
            try await testBasicTextGeneration()
            passedTests += 1
        } catch {
            print("❌ Test 1 FAILED: \(error)")
            failedTests += 1
        }
        
        // Test 2
        do {
            try await testStructuredOutput()
            passedTests += 1
        } catch {
            print("❌ Test 2 FAILED: \(error)")
            failedTests += 1
        }
        
        // Test 3
        do {
            try await testPerformance()
            passedTests += 1
        } catch {
            print("❌ Test 3 FAILED: \(error)")
            failedTests += 1
        }
        
        // Test 4
        do {
            try await testTokenLimits()
            passedTests += 1
        } catch {
            print("❌ Test 4 FAILED: \(error)")
            failedTests += 1
        }
        
        // Test 5
        do {
            try await testErrorHandling()
            passedTests += 1
        } catch {
            print("❌ Test 5 FAILED: \(error)")
            failedTests += 1
        }
        
        // Summary
        print("\n" + String(repeating: "=", count: 60))
        print("📊 Test Summary")
        print("=" + String(repeating: "=", count: 60))
        print("✅ Passed: \(passedTests)")
        print("❌ Failed: \(failedTests)")
        print("📈 Success Rate: \(passedTests)/\(passedTests + failedTests)")
        
        if failedTests == 0 {
            print("\n🎉 All tests passed! Migration to Gemini 2.5 Flash Lite is successful!")
        } else {
            print("\n⚠️  Some tests failed. Please review the errors above.")
        }
    }
}

// MARK: - Test Errors

enum TestError: Error, LocalizedError {
    case apiKeyNotConfigured
    case invalidResponse(String)
    case apiError(String)
    case parsingError(String)
    case unexpectedSuccess(String)
    
    var errorDescription: String? {
        switch self {
        case .apiKeyNotConfigured:
            return "API key not configured"
        case .invalidResponse(let message):
            return "Invalid response: \(message)"
        case .apiError(let message):
            return "API error: \(message)"
        case .parsingError(let message):
            return "Parsing error: \(message)"
        case .unexpectedSuccess(let message):
            return "Unexpected success: \(message)"
        }
    }
}

// MARK: - Usage Example
/*
 To run this test:
 
 1. In your app or test target, add:
 
 Task {
     let apiKey = APIKeys.geminiAPIKey
     let test = GeminiFlashLiteMigrationTest(apiKey: apiKey)
     await test.runAllTests()
 }
 
 2. Or run from command line:
 
 swift v12/gemini-2.5-flash-lite-migration-test.swift
 
 */

