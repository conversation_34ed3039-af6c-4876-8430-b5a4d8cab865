# V12: Recipe Detail Grounding Implementation

## Document Control
- **Maintainer:** iOS Platform – Recipe Generation & AI Integrations
- **Reviewers:** Feature Owners (Recipe Generator, Meal Plan), QA Automation, Product Team
- **Version:** 12.1
- **Last Updated:** 2025-10-01
- **Status:** ✅ Ready for Implementation + Performance Optimizations Applied
- **Review Status:** ✅ Three-Expert Consensus Approved
- **Priority:** P0 - Critical User Experience Fix
- **AI Model:** Gemini 2.5 Flash Lite (migrated from 2.5 Flash on 2025-10-01)

## Purpose
This README consolidates the documentation and engineering standards for implementing Recipe Detail Grounding, which ensures consistency between recipe ideas and recipe details. This addresses a critical UX issue where users see different servings, cooking times, and difficulty levels between the list view and detail view.

## Critical Requirements
1. **Data Flow Preservation**: Recipe objects generated in the ideas phase MUST be threaded through to detail generation without data loss.
2. **Backward Compatibility**: All changes MUST support graceful degradation for existing RecipeUIModel instances without baseRecipe.
3. **Consistency Guarantees**: Recipe Detail MUST match Recipe Idea for servings (100%), difficulty (100%), and cooking time (±10%).
4. **Prompt Engineering**: All prompt changes MUST follow semantic clarity principles - explicit constraints, no ambiguous language.
5. **Cache Stability**: Cache key generation MUST remain stable to maintain >70% hit rate.

---

## 📚 Document Index

This README is the **single source of truth** for the Recipe Detail Grounding implementation. All necessary information is contained in this document.

### 🎯 Quick Navigation

**For Executives/Product Managers:**
- Jump to: [Quick Overview](#-quick-overview)
- Jump to: [Expected Outcomes](#-expected-outcomes)
- Jump to: [Deployment Strategy](#-deployment-strategy)

**For Engineers:**
- Jump to: [Technical Standards](#technical-standards--code-requirements)
- Jump to: [Implementation Checklist](#-detailed-implementation-checklist)
- Jump to: [Code Examples](#-appendix-code-examples)

**For Technical Leads:**
- Jump to: [Three-Expert Review](#-three-expert-review-summary)
- Jump to: [Testing Strategy](#-testing-strategy)
- Jump to: [Monitoring & Observability](#-monitoring--observability)

**Additional Documents (Optional):**
- 📄 **[EXECUTIVE_SUMMARY.md](./EXECUTIVE_SUMMARY.md)** - Standalone executive summary
- 📖 **[THREE_EXPERT_REVIEW_AND_PRD.md](./THREE_EXPERT_REVIEW_AND_PRD.md)** - Detailed expert analysis
- 📝 **[RECIPE_DETAIL_GROUNDING_PLAN.md](./RECIPE_DETAIL_GROUNDING_PLAN.md)** - Original proposal

---

## 🎯 Quick Overview

### Problem
Users see recipe ideas with specific servings, cooking time, and difficulty, but when they tap to view details, these values change because the system regenerates from scratch.

### Solution
Thread the base Recipe object through the data flow so detail generation **expands** (not regenerates) the recipe.

### Impact
- ✅ 95%+ consistency between recipe ideas and details
- ✅ Kid-friendly recipes for families
- ✅ Flexible equipment usage
- ✅ Creative fusion cuisine options

### Timeline
- **Phase 1:** 1 hour (Prompt improvements)
- **Phase 2:** 4-6 hours (Recipe grounding)
- **Phase 3:** 1 hour (Cleanup)
- **Total:** 3-5 days with testing and deployment

---

## Technical Standards & Code Requirements

### 1. Architecture Principles
1. **Single Source of Truth**: Recipe objects from `RecipeGenerationService.generateMealIdeas()` are authoritative. Detail generation expands, never regenerates.
2. **Actor Isolation**: `GeminiAPIService` and `RecipeGenerationService` remain actors. All async operations respect Swift concurrency best practices.
3. **Optional Chaining**: `baseRecipe: Recipe?` in RecipeUIModel MUST be optional to support backward compatibility. All call sites MUST handle nil gracefully.
4. **Immutable Data Flow**: Recipe objects are immutable (`let` properties). Modifications create new instances rather than mutating existing ones.
5. **Sendable Compliance**: All data models crossing actor boundaries MUST conform to `Sendable` protocol.

### 2. Prompt Engineering Standards
1. **Explicit Constraints**: Use "MUST", "MAINTAIN", "DO NOT" for hard requirements. Use "suggestions", "optional", "may" for flexible guidance.
2. **Structured Output**: All prompts MUST specify exact JSON schema with field types and constraints.
3. **Consistency Anchors**: When expanding recipes, prompts MUST explicitly reference base recipe fields (title, servings, difficulty, time) as immutable constraints.
4. **Token Efficiency**: Optimize prompt length while maintaining clarity. Target <500 tokens for recipe ideas, <800 tokens for recipe details.
5. **Validation Instructions**: Include explicit validation rules in prompts (e.g., "difficulty MUST be one of: easy, medium, hard").

### 3. Data Model Requirements
1. **Codable Conformance**: All models MUST implement `Codable` for serialization. Use explicit `CodingKeys` for clarity.
2. **Identifiable Protocol**: UI models MUST conform to `Identifiable` with stable IDs. Use UUID for new instances, preserve IDs when mapping.
3. **Hashable for Caching**: Models used as cache keys MUST implement `Hashable` based on content, not identity (avoid UUID-based hashing for cache keys).
4. **Optional Fields**: New fields added to existing models MUST be optional with default values to maintain backward compatibility.
5. **Computed Properties**: Prefer computed properties over stored properties for derived data (e.g., `cookingTimeInMinutes` computed from `cookingTime` string).

### 4. Error Handling & Resilience
1. **Graceful Degradation**: If `baseRecipe` is nil, fall back to title-only detail generation. Log warning but don't fail.
2. **Structured Errors**: Use typed errors (enum with associated values) rather than generic Error types.
3. **User-Facing Messages**: Separate user-facing error messages from developer diagnostics. Include actionable guidance.
4. **Retry Logic**: Implement exponential backoff for transient failures. Maximum 2 retries for API calls.
5. **Logging Discipline**: Log all API calls with model ID, prompt length (tokens), response time, and success/failure status. Redact sensitive data.

### 5. Testing Requirements
1. **Unit Test Coverage**: Minimum 80% coverage for new code. Focus on prompt builders, data transformations, and error paths.
2. **Integration Tests**: Use `URLProtocol` mocks for API tests. Verify end-to-end flows without hitting production APIs.
3. **Snapshot Tests**: For prompt generation, use snapshot tests to detect unintended changes in prompt structure.
4. **Performance Tests**: Measure and assert on cache hit rates (>70%), API response times (<3s), and token usage (<10% increase).
5. **Manual QA Checklist**: Test with real user profiles (families with kids, equipment owners, multi-cuisine preferences).

### 6. Concurrency & Performance
1. **Actor Boundaries**: Minimize actor hops. Batch operations when possible to reduce context switching.
2. **Task Cancellation**: Support cancellation for long-running operations. Check `Task.isCancelled` at appropriate points.
3. **Cache-First Strategy**: Always check cache before making API calls. Use stable cache keys based on content, not identity.
4. **Prefetch Optimization**: Limit prefetch to top K recipes (K=3). Use `TaskGroup` for parallel prefetching with error isolation.
5. **Memory Management**: Avoid retaining large objects in closures. Use `[weak self]` or `[unowned self]` appropriately.

### 7. Configuration & Observability
1. **No Hard-Coded Values**: Model IDs, endpoints, and parameters MUST resolve from configuration (plist or environment).
2. **Feature Flags**: Use feature flags for gradual rollout. Support A/B testing between old and new detail generation.
3. **Analytics Events**: Track key metrics (consistency rate, cache hit rate, API latency, token usage) with structured events.
4. **Debug Logging**: In DEBUG builds, log full prompts and responses. In RELEASE builds, log only metadata (length, status, timing).
5. **Cost Tracking**: Log token usage for all API calls. Aggregate daily/weekly for cost analysis.

---

## Implementation Workflow

### Phase 1: Prompt Improvements (Day 1 - 1 hour)
**Priority:** P1 - Quick Wins
**Risk:** 🟢 LOW
**Dependencies:** None

**Immediate Actions:**
1. Update `RecipePreferences` struct in `Models/Recipe.swift`
2. Modify `createRecipePrompt(from:preferences:)` in `Services/RecipeGenerationService.swift`
3. Test prompt changes with sample user profiles
4. Validate output consistency

**Success Criteria:**
- [ ] Prompt includes kid-friendly constraint when `numberOfKids > 0`
- [ ] Equipment constraint uses "optional to use" language
- [ ] Cuisine constraint uses "suggestions, not strict requirements" language
- [ ] IMPORTANT note about later expansion is present

---

## 📋 Implementation Phases

### Phase 1: Prompt Improvements (1 hour)
**Status:** 🟡 Not Started
**Risk:** 🟢 LOW
**Impact:** 30% improvement in recipe relevance
**Owner:** TBD
**Reviewer:** Engineering Lead + QA

**Technical Changes:**
1. **Models/Recipe.swift** - RecipePreferences struct
   - Add `var numberOfAdults: Int = 0` field
   - Add `var numberOfKids: Int = 0` field
   - Update `init(from userPreferences:)` to extract these values
   - Maintain Sendable conformance

2. **Services/RecipeGenerationService.swift** - Prompt builder
   - Line ~194: Update cuisine constraint to "Cuisine suggestions (not strict requirements): ..."
   - Line ~212: Update equipment constraint to "Special equipment available (optional to use): ..."
   - Add kid-friendly constraint: `if preferences.numberOfKids > 0 { ... }`
   - Add IMPORTANT note before constraints section
   - Update guidelines to clarify brief instructions and no measurements in ideas

**Code Quality Requirements:**
- [ ] No force unwrapping - use optional chaining or guard statements
- [ ] All string interpolations properly escaped
- [ ] Prompt changes validated with snapshot tests
- [ ] No breaking changes to existing API signatures

**Testing Checklist:**
- [ ] Unit test: RecipePreferences initializes with numberOfAdults/numberOfKids from UserPreferences
- [ ] Unit test: Prompt builder includes kid-friendly constraint when kids > 0
- [ ] Unit test: Prompt builder includes equipment flexibility language
- [ ] Integration test: Generate recipes with kids=2, verify kid-friendly language in output
- [ ] Integration test: Generate recipes with equipment, verify optional usage
- [ ] Manual QA: Test with 5 different user profiles (no kids, 1 kid, 2+ kids, with/without equipment)

**Rollback Plan:**
- Revert `RecipePreferences` changes (backward compatible - new fields are optional)
- Revert prompt text changes in `createRecipePrompt(from:preferences:)`
- No database migrations required

**Documentation:** [IMPLEMENTATION_CHECKLIST.md](./IMPLEMENTATION_CHECKLIST.md#phase-1)

---

### Phase 2: Recipe Detail Grounding (4-6 hours)
**Status:** 🟡 Not Started
**Risk:** 🟡 MEDIUM
**Impact:** 90% reduction in recipe inconsistencies
**Owner:** TBD
**Reviewer:** Engineering Lead + QA + Product Manager

**Technical Changes:**

1. **Models/RecipeUIModel.swift** - Add baseRecipe field
   ```swift
   public let baseRecipe: Recipe?  // NEW: Thread Recipe through data flow
   ```
   - Update init method to accept `baseRecipe: Recipe? = nil`
   - Maintain Codable, Hashable, Sendable conformance
   - Ensure backward compatibility (optional field with default nil)

2. **Services/RecipeServiceAdapter.swift** - Preserve Recipe in mapping
   - Method: `mapIdeaToUI(_:pantryIngredients:meal:)`
   - Add `baseRecipe: recipe` parameter when creating RecipeUIModel
   - Verify Recipe object is not mutated during mapping

3. **Services/GeminiAPIService.swift** - Update API signature
   - Add new method: `generateRecipeDetail(baseRecipe: Recipe, pantryContext: String?, equipmentOwned: [String])`
   - Add new prompt builder: `buildRecipeDetailPrompt(baseRecipe:pantryContext:equipmentOwned:)`
   - Keep old method for backward compatibility (mark as deprecated)
   - Implement prompt that MAINTAINS base recipe constraints (servings, difficulty, time)

4. **Features/RecipeGenerator/GeneratedRecipeDetailView.swift** - Update call site
   - Method: `fetchRecipeDetail()`
   - Add conditional: if baseRecipe exists, use new API; else use old API
   - Maintain cache key stability

5. **Services/RecipeServiceAdapter.swift** - Update prefetch
   - Struct: `DefaultRecipeDetailPrefetcher`
   - Method: `prefetchDetails(for:pantryIngredients:userPreferences:)`
   - Add conditional: if baseRecipe exists, use new API; else use old API

**Code Quality Requirements:**
- [ ] All new methods documented with Swift DocC comments
- [ ] Actor isolation verified - no data races
- [ ] Optional unwrapping uses guard statements with early returns
- [ ] Error handling includes user-facing messages
- [ ] Logging includes model ID, prompt tokens, response time
- [ ] No force casts or force unwraps
- [ ] All async methods support cancellation

**Testing Checklist:**
- [ ] Unit test: RecipeUIModel serialization with baseRecipe (Codable)
- [ ] Unit test: RecipeUIModel hashing with baseRecipe (Hashable)
- [ ] Unit test: New prompt builder includes all base recipe fields
- [ ] Unit test: New prompt builder enforces MAINTAIN constraints
- [ ] Integration test: Generate idea → view detail → verify servings match (100%)
- [ ] Integration test: Generate idea → view detail → verify difficulty match (100%)
- [ ] Integration test: Generate idea → view detail → verify time within ±10%
- [ ] Integration test: Generate idea → view detail → verify ingredients are superset
- [ ] Integration test: Old RecipeUIModel (baseRecipe=nil) falls back gracefully
- [ ] Performance test: Cache hit rate remains >70%
- [ ] Performance test: API response time <3s
- [ ] Performance test: Token usage increase <10%
- [ ] Manual QA: Test 10 different recipes for consistency
- [ ] Manual QA: Test with cached recipes (verify cache still works)
- [ ] Manual QA: Test with old app data (verify backward compatibility)

**Rollback Plan:**
- Keep old `generateRecipeDetail(title:...)` method active
- Update call sites to use old method
- Remove baseRecipe from RecipeUIModel (optional field, safe to ignore)
- Revert prompt changes
- Cache remains functional (keys unchanged)

**Documentation:** [IMPLEMENTATION_CHECKLIST.md](./IMPLEMENTATION_CHECKLIST.md#phase-2)

---

### Phase 3: Cleanup (1 hour)
**Status:** 🟡 Not Started
**Risk:** 🟢 LOW
**Impact:** Improved code maintainability
**Owner:** TBD
**Reviewer:** Engineering Lead

**Technical Changes:**

1. **Services/RecipeGenerationService.swift** - Remove unused code
   - Delete method: `createRecipePrompt(from: [String])` (lines ~156-182)
   - Delete method: `generateRecipes(from: [String])` (lines ~7-45)
   - Verify no references exist in codebase (use Xcode "Find in Project")
   - Run static analysis to confirm no dead code warnings

2. **Prompts/ALL_PROMPTS.md** - Update documentation
   - Mark "Recipe ideas – simple" as DEPRECATED
   - Add section for "Recipe Ideas – Enhanced" with updated prompt
   - Add section for "Recipe Detail – Grounded" with new prompt
   - Include examples of output for each prompt

3. **v12/RECIPE_DETAIL_GROUNDING_PLAN.md** - Update status
   - Change status from "Proposed" to "Implemented"
   - Add implementation date and version
   - Add link to THREE_EXPERT_REVIEW_AND_PRD.md

4. **README.md** (project root) - Update architecture docs
   - Update recipe generation flow description
   - Mention consistency improvements
   - Update API documentation

**Code Quality Requirements:**
- [ ] No orphaned imports after deletion
- [ ] No compiler warnings after cleanup
- [ ] All documentation links verified (no broken links)
- [ ] Version numbers updated consistently

**Testing Checklist:**
- [ ] Full test suite passes (unit + integration)
- [ ] No references to deleted methods in codebase
- [ ] Static analysis shows no warnings
- [ ] Documentation builds without errors
- [ ] Manual QA: Smoke test all recipe features
- [ ] Manual QA: Verify no regressions in other features

**Rollback Plan:**
- Restore deleted methods from git history
- Revert documentation changes
- No functional impact (only cleanup)

**Documentation:** [IMPLEMENTATION_CHECKLIST.md](./IMPLEMENTATION_CHECKLIST.md#phase-3)

---

## Code Review & Quality Gates

### Pre-Implementation Checklist
- [ ] All team members have reviewed the PRD and technical standards
- [ ] Feature flag configuration prepared for gradual rollout
- [ ] Analytics events defined and instrumentation plan ready
- [ ] Rollback procedures documented and tested
- [ ] QA test plan reviewed and approved

### Code Review Requirements
All PRs for this feature MUST:
1. Reference the phase and specific requirement (e.g., "Phase 2, Task 2.3: Update GeminiAPIService")
2. Include unit tests with >80% coverage for new code
3. Include integration tests for API changes
4. Pass all existing tests (no regressions)
5. Include before/after examples for prompt changes
6. Document any breaking changes or migration steps
7. Require sign-off from at least one senior engineer and one QA reviewer

### Merge Criteria
- [ ] All automated tests pass (unit, integration, UI)
- [ ] Code coverage meets minimum threshold (80% for new code)
- [ ] No new compiler warnings introduced
- [ ] Static analysis passes (SwiftLint, no critical issues)
- [ ] Performance benchmarks meet targets (cache hit rate, API latency)
- [ ] Documentation updated (inline comments, README, API docs)
- [ ] QA sign-off obtained for manual testing

### Post-Merge Validation
- [ ] Staging deployment successful
- [ ] Smoke tests pass on staging
- [ ] Metrics dashboard shows expected behavior
- [ ] No error spikes in logs
- [ ] Performance metrics within acceptable range

---

## Collaboration & Communication

### Daily Standups
- Report progress on current phase
- Highlight blockers or risks
- Coordinate with dependent teams (QA, Product)

### Phase Completion Reviews
After each phase:
1. Demo functionality to stakeholders
2. Review metrics and success criteria
3. Collect feedback and adjust next phase if needed
4. Update project tracker with actual vs. estimated time

### Incident Response
If critical issues arise:
1. Immediately notify Engineering Lead and Product Manager
2. Assess severity and user impact
3. Execute rollback if necessary (follow rollback plan)
4. Conduct post-mortem and update documentation

---

## 🔍 Three-Expert Review Summary

### Expert 1: Code Auditor
**Role:** Review current codebase architecture and identify technical constraints

**Key Findings:**
- ✅ UserPreferences already has `numberOfAdults` and `numberOfKids` fields
- ✅ Recipe model is comprehensive and ready
- ⚠️ RecipeUIModel needs `baseRecipe: Recipe?` field added
- ⚠️ GeminiAPIService API signature needs update
- ⚠️ Cache key generation needs adjustment

**Risk Assessment:** 🟡 MEDIUM (API changes affect 3+ call sites)

---

### Expert 2: Product Strategist
**Role:** Refine requirements and prioritize features based on user impact

**Priority Matrix:**
| Feature | Impact | Effort | Priority |
|---------|--------|--------|----------|
| Recipe Detail Grounding | CRITICAL | 4-6h | P0 |
| Kid-friendly adjustments | HIGH | 30min | P1 |
| Equipment flexibility | HIGH | 15min | P1 |
| Cuisine flexibility | MEDIUM | 15min | P2 |

**Recommendation:** Phased approach with quick wins first

---

### Expert 3: Implementation Lead
**Role:** Synthesize findings into an executable implementation plan

**Strategy:**
- 3 phases over 3-5 days
- Feature flags for safe rollout
- Backward compatibility maintained
- Comprehensive testing at each phase

**Success Metrics:**
- Recipe consistency: 95%+
- Cache hit rate: >70%
- API cost increase: <10%
- User satisfaction: 4.5+ stars

---

## 📊 Success Criteria

### Must Have (P0)
- ✅ Recipe Detail servings MUST match Recipe Idea servings (100%)
- ✅ Recipe Detail difficulty MUST match Recipe Idea difficulty (100%)
- ✅ Recipe Detail time MUST be within ±10% of Recipe Idea time (95%+)
- ✅ Recipe Detail ingredients MUST be superset of Recipe Idea ingredients (100%)

### Should Have (P1)
- ✅ Kid-friendly recipes when `numberOfKids > 0` (100%)
- ✅ Equipment treated as optional enhancement (100%)

### Nice to Have (P2)
- ✅ Cuisine flexibility and fusion options (100%)
- ✅ Code cleanup and documentation (100%)

---

## 🧪 Testing Strategy

### Unit Tests (Target: 80%+ coverage)

**Phase 1 Tests:**
- [ ] `RecipePreferencesTests.swift`
  - Test initialization from UserPreferences with numberOfAdults/numberOfKids
  - Test default values when fields are missing
  - Test Sendable conformance (compile-time check)

- [ ] `RecipePromptBuilderTests.swift`
  - Test prompt includes kid-friendly constraint when numberOfKids > 0
  - Test prompt excludes kid-friendly constraint when numberOfKids = 0
  - Test equipment constraint uses "optional to use" language
  - Test cuisine constraint uses "suggestions" language
  - Test IMPORTANT note is present
  - Snapshot test: Verify prompt structure hasn't changed unintentionally

**Phase 2 Tests:**
- [ ] `RecipeUIModelTests.swift`
  - Test Codable encoding/decoding with baseRecipe
  - Test Codable encoding/decoding without baseRecipe (backward compatibility)
  - Test Hashable conformance with baseRecipe
  - Test Sendable conformance (compile-time check)

- [ ] `GeminiAPIServiceTests.swift`
  - Test new generateRecipeDetail(baseRecipe:) method
  - Test prompt builder includes all base recipe fields
  - Test prompt builder enforces MAINTAIN constraints
  - Test old generateRecipeDetail(title:) method still works (deprecated)
  - Mock URLSession to verify request structure

- [ ] `RecipeServiceAdapterTests.swift`
  - Test mapIdeaToUI preserves baseRecipe
  - Test Recipe object is not mutated during mapping

**Phase 3 Tests:**
- [ ] `RecipeGenerationServiceTests.swift`
  - Verify deleted methods are not referenced
  - Verify no orphaned code after cleanup

### Integration Tests (End-to-End Flows)

- [ ] `RecipeConsistencyIntegrationTests.swift`
  - Test: Generate idea → view detail → verify servings match (100%)
  - Test: Generate idea → view detail → verify difficulty match (100%)
  - Test: Generate idea → view detail → verify time within ±10%
  - Test: Generate idea → view detail → verify ingredients are superset
  - Use URLProtocol mocks to simulate API responses

- [ ] `BackwardCompatibilityIntegrationTests.swift`
  - Test: Load old RecipeUIModel (baseRecipe=nil) → view detail → verify fallback works
  - Test: Deserialize old JSON without baseRecipe field → verify no crash

- [ ] `CacheIntegrationTests.swift`
  - Test: Generate detail → cache → retrieve from cache → verify hit
  - Test: Cache key stability with baseRecipe
  - Test: Cache key stability without baseRecipe (backward compatibility)

### Performance Tests (Benchmarks)

- [ ] `RecipeDetailPerformanceTests.swift`
  - Measure: API response time for detail generation (target: <3s)
  - Measure: Cache hit rate over 100 requests (target: >70%)
  - Measure: Token usage increase vs. baseline (target: <10%)
  - Measure: Memory usage during detail generation (target: <50MB)

### Manual QA Checklist

**Phase 1 Manual Tests:**
- [ ] Test with user profile: 0 kids → verify no kid-friendly language
- [ ] Test with user profile: 1 kid → verify kid-friendly language
- [ ] Test with user profile: 2+ kids → verify kid-friendly language
- [ ] Test with equipment: air fryer → verify optional usage language
- [ ] Test with equipment: none → verify basic equipment assumed
- [ ] Test with cuisines: Italian, Mexican → verify fusion options

**Phase 2 Manual Tests:**
- [ ] Generate 10 recipes → view details → verify consistency for all 10
- [ ] Test with cached recipes → verify cache still works
- [ ] Test with old app data → verify backward compatibility
- [ ] Test with slow network → verify loading states
- [ ] Test with API failure → verify error messages

**Phase 3 Manual Tests:**
- [ ] Smoke test: Quick recipe generation
- [ ] Smoke test: Meal plan generation
- [ ] Smoke test: Recipe detail view
- [ ] Smoke test: Recipe search
- [ ] Regression test: All other features work as expected

### Automated QA (CI/CD Pipeline)

- [ ] Run full test suite on every commit
- [ ] Run performance benchmarks on every PR
- [ ] Run static analysis (SwiftLint) on every PR
- [ ] Generate code coverage report on every PR
- [ ] Block merge if coverage drops below 80%
- [ ] Block merge if performance benchmarks fail

---

## ⚠️ Risk Mitigation

### Risk 1: Cache Invalidation
**Impact:** Cache hit rate drops, API costs increase  
**Probability:** MEDIUM  
**Mitigation:** Implement dual-key lookup (try new key, fallback to old)  
**Rollback:** Keep old API method as fallback

### Risk 2: Backward Compatibility
**Impact:** Old RecipeUIModel instances break  
**Probability:** LOW  
**Mitigation:** Make `baseRecipe` optional, graceful degradation  
**Rollback:** Feature flag to disable grounding

### Risk 3: API Cost Increase
**Impact:** Budget overrun  
**Probability:** LOW  
**Mitigation:** Monitor token usage, optimize prompt length  
**Rollback:** Revert to title-only if costs spike >20%

---

## 📅 Deployment Strategy

### Pre-Deployment Checklist
- [ ] All phases completed and tested
- [ ] Code reviewed and approved
- [ ] QA sign-off obtained
- [ ] Feature flag configured
- [ ] Rollback plan documented and tested
- [ ] Monitoring dashboards configured
- [ ] On-call engineer assigned
- [ ] Stakeholders notified of deployment schedule

### Stage 1: Internal Testing (Day 1)
**Environment:** Staging
**Traffic:** Internal team only
**Duration:** 4-8 hours

**Actions:**
- Deploy Phase 1 to staging environment
- Run automated test suite
- Manual testing with 10+ user profiles (various family sizes, equipment, cuisines)
- Validate prompt improvements with real API calls
- Check logs for errors or warnings

**Success Criteria:**
- [ ] All automated tests pass
- [ ] No errors in logs
- [ ] Prompt changes produce expected output
- [ ] Performance metrics within acceptable range

**Go/No-Go Decision:** Engineering Lead approval required

---

### Stage 2: Beta Release (Day 2-3)
**Environment:** Production
**Traffic:** 10% of users (feature flag)
**Duration:** 24-48 hours

**Actions:**
- Deploy Phase 2 to production with feature flag (10% enabled)
- Monitor consistency metrics in real-time
- Monitor cache hit rate and API costs
- Collect user feedback through in-app surveys
- Watch for error spikes or performance degradation

**Monitoring Metrics:**
- Recipe consistency rate (target: >95%)
- Cache hit rate (target: >70%)
- API response time (target: <3s)
- Token usage increase (target: <10%)
- Error rate (target: <0.1%)
- User satisfaction (target: >4.5 stars)

**Success Criteria:**
- [ ] Consistency rate >95%
- [ ] Cache hit rate >70%
- [ ] No error spikes
- [ ] User feedback positive
- [ ] API costs within budget

**Go/No-Go Decision:** Engineering Lead + Product Manager approval required

---

### Stage 3: Gradual Rollout (Day 4-5)
**Environment:** Production
**Traffic:** 10% → 50% → 100%
**Duration:** 24-48 hours

**Actions:**
- Increase feature flag to 50% if Stage 2 metrics are good
- Monitor for 12-24 hours
- Increase to 100% if metrics remain stable
- Continue monitoring for 24 hours after 100%

**Monitoring Metrics:** (same as Stage 2)

**Rollback Triggers:**
- Consistency rate drops below 90%
- Cache hit rate drops below 60%
- Error rate exceeds 0.5%
- API costs increase >20%
- User satisfaction drops below 4.0 stars

**Success Criteria:**
- [ ] All metrics stable at 50% traffic
- [ ] All metrics stable at 100% traffic
- [ ] No user complaints or support tickets
- [ ] API costs within budget

**Go/No-Go Decision:** Engineering Lead + Product Manager approval required

---

### Stage 4: Cleanup & Finalization (Day 6)
**Environment:** Production
**Traffic:** 100%
**Duration:** 2-4 hours

**Actions:**
- Deploy Phase 3 (code cleanup)
- Remove feature flag (make new behavior default)
- Archive old code
- Update documentation
- Publish release notes
- Notify stakeholders of completion

**Success Criteria:**
- [ ] All tests pass after cleanup
- [ ] No regressions detected
- [ ] Documentation updated
- [ ] Release notes published

---

### Post-Deployment Monitoring (Week 1-2)

**Daily Monitoring:**
- Check consistency metrics dashboard
- Review error logs
- Monitor API costs
- Track user feedback

**Weekly Review:**
- Analyze metrics trends
- Review user feedback
- Identify areas for improvement
- Plan follow-up optimizations

**Success Metrics (Week 1):**
- Recipe consistency rate: >95%
- Cache hit rate: >70%
- User satisfaction: >4.5 stars
- API cost increase: <10%
- Support tickets: <5 related to recipe inconsistency

---

## � Monitoring & Observability

### Key Metrics Dashboard

**Recipe Consistency Metrics:**
- `recipe.consistency.servings_match_rate` (target: 100%)
- `recipe.consistency.difficulty_match_rate` (target: 100%)
- `recipe.consistency.time_match_rate` (target: 95%+, within ±10%)
- `recipe.consistency.ingredients_superset_rate` (target: 100%)

**Performance Metrics:**
- `recipe.detail.api_response_time_p50` (target: <2s)
- `recipe.detail.api_response_time_p95` (target: <3s)
- `recipe.detail.cache_hit_rate` (target: >70%)
- `recipe.detail.token_usage_per_request` (baseline + <10%)

**Error Metrics:**
- `recipe.detail.error_rate` (target: <0.1%)
- `recipe.detail.fallback_rate` (when baseRecipe is nil)
- `recipe.detail.api_failure_rate` (target: <0.5%)

**Business Metrics:**
- `recipe.detail.user_satisfaction_rating` (target: >4.5 stars)
- `recipe.detail.view_completion_rate` (users who view full detail)
- `recipe.api.daily_cost` (budget monitoring)

### Logging Standards

**Structured Log Format:**
```swift
// Example log entry
{
  "timestamp": "2025-09-30T10:15:30Z",
  "level": "INFO",
  "service": "GeminiAPIService",
  "method": "generateRecipeDetail",
  "model_id": "gemini-2.5-flash",
  "has_base_recipe": true,
  "prompt_tokens": 450,
  "response_tokens": 1200,
  "total_tokens": 1650,
  "response_time_ms": 2340,
  "cache_hit": false,
  "status": "success"
}
```

**Log Levels:**
- **DEBUG:** Full prompts and responses (DEBUG builds only)
- **INFO:** Request metadata (model, tokens, timing, status)
- **WARNING:** Fallback usage, cache misses, slow responses
- **ERROR:** API failures, parsing errors, validation failures

**Sensitive Data Handling:**
- ✅ Log: Model IDs, token counts, response times, status codes
- ❌ Redact: API keys, user IDs, personal information
- ❌ Redact: Full recipe content in production logs

### Alerting Rules

**Critical Alerts (Page on-call engineer):**
- Error rate >1% for 5 minutes
- API response time p95 >5s for 10 minutes
- Consistency rate <80% for 15 minutes
- API cost spike >50% above baseline

**Warning Alerts (Slack notification):**
- Cache hit rate <60% for 30 minutes
- Token usage increase >15% for 1 hour
- Fallback rate >20% for 30 minutes
- User satisfaction <4.0 stars

**Info Alerts (Dashboard only):**
- Consistency rate 90-95%
- Cache hit rate 60-70%
- Token usage increase 10-15%

### Debug Tools

**Feature Flags:**
- `recipe_detail_grounding_enabled` (default: true after rollout)
- `recipe_detail_grounding_beta` (for gradual rollout)
- `recipe_detail_logging_verbose` (for debugging)

**Debug Menu (DEBUG builds only):**
- View last generated prompt
- View last API response
- Force cache clear
- Toggle between old/new detail generation
- View consistency metrics for current session

---

## �📞 Stakeholder Communication

### Before Implementation
- [ ] Engineering Lead review and approval
- [ ] Product Manager review and approval
- [ ] QA Lead review and approval
- [ ] Resource allocation confirmed
- [ ] Timeline confirmed

### During Implementation
- [ ] Daily standup updates
- [ ] Phase completion notifications
- [ ] Issue escalation as needed

### After Implementation
- [ ] Metrics report (consistency, performance, costs)
- [ ] User feedback summary
- [ ] Lessons learned documentation

---

## 📚 Related Documentation

### Internal Documents
- [Original Plan](./RECIPE_DETAIL_GROUNDING_PLAN.md) - Detailed technical plan
- [Expert Review](./THREE_EXPERT_REVIEW_AND_PRD.md) - Three-expert analysis and PRD
- [Executive Summary](./EXECUTIVE_SUMMARY.md) - High-level overview
- [Implementation Checklist](./IMPLEMENTATION_CHECKLIST.md) - Step-by-step guide

### External References
- Recipe Generation Service: `Services/RecipeGenerationService.swift`
- Gemini API Service: `Services/GeminiAPIService.swift`
- Recipe Models: `Models/Recipe.swift`
- Recipe UI Models: `Models/RecipeUIModel.swift`
- User Preferences: `Models/UserPreferences.swift`

---

## 🎉 Expected Outcomes

After successful implementation:

### User Experience
- ✅ Consistent recipe information from idea to detail
- ✅ Kid-friendly recipes for families with children
- ✅ Flexible equipment usage (not forced to use special equipment)
- ✅ Creative fusion cuisine options

### Technical Quality
- ✅ Cleaner codebase (unused code removed)
- ✅ Better data flow architecture
- ✅ Improved maintainability
- ✅ Backward compatibility maintained

### Business Impact
- ✅ Higher user satisfaction (target: 4.5+ stars)
- ✅ Increased trust in recipe generation
- ✅ Better API efficiency (no redundant regeneration)
- ✅ Reduced support tickets related to recipe inconsistencies

---

## � Technical Debt & Future Improvements

### Known Limitations (Accepted for V12)
1. **Cache Key Stability:** Current cache keys based on title + pantry + preferences. Consider content-based hashing for better stability.
2. **Token Usage Tracking:** Basic logging implemented. Consider more sophisticated cost attribution per user/feature.
3. **Prompt Versioning:** Prompts are inline strings. Consider externalizing to configuration for easier A/B testing.
4. **Error Recovery:** Basic retry logic. Consider circuit breaker pattern for API resilience.

### Future Enhancements (Post-V12)
1. **Prompt Optimization:** Use prompt engineering tools to reduce token usage while maintaining quality.
2. **Caching Strategy:** Implement multi-tier caching (memory + disk + CDN) for better performance.
3. **Personalization:** Learn from user preferences over time to improve recipe suggestions.
4. **Offline Support:** Cache more recipes locally for offline viewing.
5. **Analytics Dashboard:** Build dedicated dashboard for recipe consistency and quality metrics.

### Technical Debt Tracking
- [ ] Refactor prompt builders into separate service (currently in RecipeGenerationService)
- [ ] Extract cache logic into dedicated CacheService (currently in RecipeDetailCache)
- [ ] Add comprehensive error types (currently using generic Error)
- [ ] Improve test coverage for edge cases (currently ~80%, target 90%+)
- [ ] Document API contracts with OpenAPI/Swagger specs

---

## 📚 Reference Documentation

### Apple Documentation
- [Swift Concurrency](https://docs.swift.org/swift-book/LanguageGuide/Concurrency.html) - Actor isolation, async/await
- [Codable](https://developer.apple.com/documentation/swift/codable) - Serialization best practices
- [URLSession](https://developer.apple.com/documentation/foundation/urlsession) - Network requests
- [XCTest](https://developer.apple.com/documentation/xctest) - Unit testing framework

### Google Gemini Documentation
- [Gemini API Reference](https://ai.google.dev/api/rest) - API endpoints and parameters
- [Gemini 2.5 Flash Lite](https://ai.google.dev/gemini-api/docs/models/gemini#gemini-2.5-flash-lite) - **Current model** (ultra fast, cost-efficient)
- [Gemini 2.5 Flash](https://ai.google.dev/gemini-api/docs/models/gemini-v2) - Previous model capabilities
- [Token Counting](https://ai.google.dev/gemini-api/docs/tokens) - Token usage and pricing
- [Content Filtering](https://ai.google.dev/gemini-api/docs/safety-settings) - Safety settings

### V12 Performance Optimizations
- [Gemini 2.5 Flash Lite Migration Report](./Gemini-2.5-Flash-Lite-Migration-Report.md) - Migration from 2.5 Flash to 2.5 Flash Lite (2025-10-01)
- [Performance Optimization Report](./V12性能优化和Bug修复完整报告.md) - Complete performance optimization and bug fixes
- [Migration Test Suite](./gemini-2.5-flash-lite-migration-test.swift) - Automated tests for Flash Lite migration

### Internal Documentation
- [V11 Gemini Migration](../v11/README.md) - Previous migration to Gemini 2.5 Flash
- [Recipe Generation Architecture](../docs/architecture/recipe-generation.md) - System design
- [Prompt Engineering Guide](../docs/guides/prompt-engineering.md) - Best practices
- [Testing Standards](../docs/standards/testing.md) - Testing requirements

---

## �📝 Version History

| Version | Date | Author | Changes |
|---------|------|--------|---------|
| 12.0 | 2025-09-30 | Three-Expert Panel | Initial release with complete documentation |
| 12.1 | TBD | TBD | Post-deployment optimizations and bug fixes |

---

## 📋 Appendix: Code Examples

### Example 1: RecipePreferences with Family Composition

```swift
// Models/Recipe.swift
struct RecipePreferences: Sendable {
    var cookingTimeInMinutes: Int
    var numberOfServings: Int
    var dietaryRestrictions: [String]
    var allergiesAndIntolerances: [String]
    var strictExclusions: [String]
    var customStrictExclusions: [String]
    var respectRestrictions: Bool
    var cuisines: [String] = []
    var additionalRequest: String? = nil
    var equipmentOwned: [String] = []
    var targetMealType: MealType? = nil
    var targetDishCount: Int? = nil

    // NEW: Family composition
    var numberOfAdults: Int = 0
    var numberOfKids: Int = 0

    /// Initialize from UserPreferences
    init(from userPreferences: UserPreferences, cookingTime: Int) {
        self.cookingTimeInMinutes = cookingTime
        self.numberOfServings = userPreferences.familySize
        self.dietaryRestrictions = userPreferences.dietaryRestrictions.map { $0.rawValue }
        self.allergiesAndIntolerances = userPreferences.allergiesIntolerances.map { $0.rawValue }
        self.strictExclusions = userPreferences.strictExclusions.map { $0.rawValue }
        self.customStrictExclusions = userPreferences.customStrictExclusions
        self.respectRestrictions = userPreferences.respectRestrictions

        // NEW: Extract family composition
        self.numberOfAdults = userPreferences.numberOfAdults
        self.numberOfKids = userPreferences.numberOfKids
    }
}
```

### Example 2: RecipeUIModel with baseRecipe

```swift
// Models/RecipeUIModel.swift
public struct RecipeUIModel: Identifiable, Hashable, Codable, Sendable {
    public let id: String
    public let title: String
    public let subtitle: String?
    public let estimatedTime: Int?
    public let imageURL: String?
    public let ingredientsFromPantry: [String]?
    public let additionalIngredients: [String]?
    public let difficulty: String?
    public let mealType: MealType?
    public let dayIndex: Int?
    public let servings: Int?
    public let cuisine: String?
    public let scheduledDate: Date?

    // NEW: Base recipe for detail expansion
    public let baseRecipe: Recipe?

    public init(
        id: String,
        title: String,
        subtitle: String? = nil,
        estimatedTime: Int? = nil,
        imageURL: String? = nil,
        ingredientsFromPantry: [String]? = nil,
        additionalIngredients: [String]? = nil,
        difficulty: String? = nil,
        mealType: MealType? = nil,
        dayIndex: Int? = nil,
        servings: Int? = nil,
        cuisine: String? = nil,
        scheduledDate: Date? = nil,
        baseRecipe: Recipe? = nil  // NEW
    ) {
        self.id = id
        self.title = title
        self.subtitle = subtitle
        self.estimatedTime = estimatedTime
        self.imageURL = imageURL
        self.ingredientsFromPantry = ingredientsFromPantry
        self.additionalIngredients = additionalIngredients
        self.difficulty = difficulty
        self.mealType = mealType
        self.dayIndex = dayIndex
        self.servings = servings
        self.cuisine = cuisine
        self.scheduledDate = scheduledDate
        self.baseRecipe = baseRecipe  // NEW
    }
}
```

### Example 3: Grounded Detail Generation

```swift
// Services/GeminiAPIService.swift
actor GeminiAPIService {

    /// Generate detailed recipe by expanding base recipe (NEW)
    func generateRecipeDetail(
        baseRecipe: Recipe,
        pantryContext: String? = nil,
        equipmentOwned: [String] = []
    ) async throws -> RecipeDetail {
        let prompt = buildRecipeDetailPrompt(
            baseRecipe: baseRecipe,
            pantryContext: pantryContext,
            equipmentOwned: equipmentOwned
        )

        let response = try await callGeminiAPI(prompt: prompt)
        return try parseRecipeDetailResponse(response)
    }

    /// Build prompt that expands base recipe (NEW)
    private func buildRecipeDetailPrompt(
        baseRecipe: Recipe,
        pantryContext: String?,
        equipmentOwned: [String]
    ) -> String {
        var prompt = """
        You are a professional chef. Expand this recipe into detailed steps.

        RECIPE TO EXPAND:
        Title: "\(baseRecipe.recipeTitle)"
        Servings: \(baseRecipe.servings)
        Difficulty: \(baseRecipe.difficulty.rawValue)
        Cooking Time: \(baseRecipe.cookingTime)

        Requirements:
        1. MAINTAIN servings: \(baseRecipe.servings)
        2. MAINTAIN difficulty: \(baseRecipe.difficulty.rawValue)
        3. MAINTAIN cooking time: \(baseRecipe.cookingTime)
        4. EXPAND ingredients with measurements
        5. Generate 6-12 detailed steps

        Return JSON with exact format...
        """

        // Add equipment if available
        if !equipmentOwned.isEmpty {
            prompt += """

            Special equipment available (optional): \(equipmentOwned.joined(separator: ", "))
            """
        }

        return prompt
    }
}
```

---

## 📋 Detailed Implementation Checklist

**Estimated Total Time:** 6-8 hours
**Status:** 🟡 Ready to Start

---

### PHASE 1: PROMPT IMPROVEMENTS (1 hour)

#### Task 1.1: Update RecipePreferences Model (10 min)
**File:** `Models/Recipe.swift`

- [ ] Add `var numberOfAdults: Int = 0` field to RecipePreferences struct
- [ ] Add `var numberOfKids: Int = 0` field to RecipePreferences struct
- [ ] Update `init(from userPreferences:)` method:
  ```swift
  self.numberOfAdults = userPreferences.numberOfAdults
  self.numberOfKids = userPreferences.numberOfKids
  ```
- [ ] Update manual `init()` to include these parameters
- [ ] Compile and verify no errors

**Verification:**
```swift
// Test code
let prefs = RecipePreferences(from: userPreferences, cookingTime: 30)
print(prefs.numberOfAdults) // Should print user's adult count
print(prefs.numberOfKids)   // Should print user's kid count
```

---

#### Task 1.2: Update Recipe Ideas Prompt (30 min)
**File:** `Services/RecipeGenerationService.swift`
**Method:** `createRecipePrompt(from:preferences:)`

- [ ] **Line ~194:** Change cuisine constraint
  ```swift
  // OLD:
  constraints.append("Preferred cuisines: \(preferences.cuisines.joined(separator: ", ")).")

  // NEW:
  constraints.append("Cuisine suggestions (not strict requirements): \(preferences.cuisines.joined(separator: ", ")). You may use one, mix multiple, create fusion dishes, or draw inspiration from these styles.")
  ```

- [ ] **Line ~212:** Change equipment constraint
  ```swift
  // OLD:
  if !preferences.equipmentOwned.isEmpty {
      constraints.append("Available equipment only: \(preferences.equipmentOwned.joined(separator: ", ")).")
  }

  // NEW:
  if !preferences.equipmentOwned.isEmpty {
      constraints.append("Special equipment available (optional to use): \(preferences.equipmentOwned.joined(separator: ", ")). You may also use basic kitchen equipment (microwave, oven, stovetop).")
  } else {
      constraints.append("Assume basic kitchen equipment is available (microwave, oven, stovetop).")
  }
  ```

- [ ] **After line ~213:** Add kid-friendly constraint
  ```swift
  // NEW:
  if preferences.numberOfKids > 0 {
      constraints.append("Family includes \(preferences.numberOfKids) kid(s) - make recipes kid-friendly with milder spices, familiar flavors, and simpler textures.")
  }
  ```

- [ ] **Line ~227:** Add IMPORTANT note before constraints
  ```swift
  return """
  Generate at least \(dishTarget) healthy recipes using these ingredients: \(ingredients.joined(separator: ", ")).
  Target servings: \(preferences.numberOfServings). Target cooking time: ~\(preferences.cookingTimeInMinutes) minutes per recipe.

  IMPORTANT: These recipe ideas will be expanded into detailed recipes later. Ensure all fields (title, servings, difficulty, cooking time) are realistic and consistent, as they will be used as constraints for the detailed version.

  \(constraintsText)Return the response as a JSON array...
  ```

- [ ] **Line ~248:** Update guidelines section
  ```swift
  Guidelines:
  - Only use the provided ingredients (plus common staples like salt, pepper, oil)
  - Absolutely avoid any listed allergens or strict exclusions
  - Ensure difficulty is one of: "easy", "medium", "hard"
  - Keep instructions brief (3-5 high-level steps) - detailed steps will be generated later
  - Ingredients list should include core ingredients without measurements (measurements will be added in detail phase)
  - Cooking time should be realistic and match the difficulty level
  - For cuisines: feel free to create fusion dishes, use one style, or draw inspiration - not all selected cuisines need to appear in every dish
  ```

- [ ] Compile and verify no errors

---

#### Task 1.3: Testing Phase 1 (20 min)

- [ ] **Test 1:** Generate recipes with kids
  - Set `numberOfKids = 2` in user preferences
  - Generate recipes
  - Verify prompt includes: "Family includes 2 kid(s) - make recipes kid-friendly..."
  - Check generated recipes for mild/familiar language

- [ ] **Test 2:** Generate recipes with equipment
  - Set `equipmentOwned = ["air fryer", "slow cooker"]`
  - Generate recipes
  - Verify prompt includes: "Special equipment available (optional to use): air fryer, slow cooker..."
  - Check recipes can use equipment OR basics

- [ ] **Test 3:** Generate recipes with multiple cuisines
  - Set `cuisines = ["Italian", "Mexican", "Japanese"]`
  - Generate recipes
  - Verify prompt includes: "Cuisine suggestions (not strict requirements)..."
  - Check for fusion dishes or single-cuisine focus

- [ ] **Test 4:** Verify IMPORTANT note
  - Generate any recipes
  - Check prompt includes: "IMPORTANT: These recipe ideas will be expanded into detailed recipes later..."

---

### PHASE 2: RECIPE DETAIL GROUNDING (4-6 hours)

#### Task 2.1: Update RecipeUIModel (15 min)
**File:** `Models/RecipeUIModel.swift`

- [ ] Add `public let baseRecipe: Recipe?` field to struct
- [ ] Add `baseRecipe: Recipe? = nil` parameter to init method
- [ ] Assign `self.baseRecipe = baseRecipe` in init body
- [ ] Compile and verify Codable conformance still works
- [ ] Run unit tests for RecipeUIModel

**Verification:**
```swift
// Test code
let model = RecipeUIModel(
    id: "test",
    title: "Test Recipe",
    baseRecipe: sampleRecipe
)
print(model.baseRecipe?.recipeTitle) // Should print "Test Recipe"
```

---

#### Task 2.2: Update RecipeServiceAdapter Mapping (15 min)
**File:** `Services/RecipeServiceAdapter.swift`
**Method:** `mapIdeaToUI(_:pantryIngredients:meal:)`

- [ ] Find the return statement creating RecipeUIModel
- [ ] Add `baseRecipe: recipe` parameter to RecipeUIModel init
  ```swift
  return RecipeUIModel(
      id: idea.id.uuidString,
      title: recipe.recipeTitle,
      subtitle: recipe.description,
      estimatedTime: recipe.cookingTimeInMinutes,
      imageURL: nil,
      ingredientsFromPantry: pantryIngs,
      additionalIngredients: additional.isEmpty ? nil : additional,
      difficulty: recipe.difficulty.rawValue,
      mealType: meal,
      dayIndex: nil,
      servings: recipe.servings,
      cuisine: nil,
      scheduledDate: nil,
      baseRecipe: recipe  // NEW
  )
  ```
- [ ] Compile and verify no errors

---

#### Task 2.3: Update GeminiAPIService (1 hour)
**File:** `Services/GeminiAPIService.swift`

**Step 2.3a: Add new method signature**
- [ ] Add new `generateRecipeDetail` method with baseRecipe parameter:
  ```swift
  func generateRecipeDetail(
      baseRecipe: Recipe,
      pantryContext: String? = nil,
      equipmentOwned: [String] = []
  ) async throws -> RecipeDetail {
      let prompt = buildRecipeDetailPrompt(
          baseRecipe: baseRecipe,
          pantryContext: pantryContext,
          equipmentOwned: equipmentOwned
      )
      let response = try await callGeminiAPI(prompt: prompt)
      return try parseRecipeDetailResponse(response)
  }
  ```

**Step 2.3b: Add new prompt builder**
- [ ] Add new `buildRecipeDetailPrompt` method with baseRecipe parameter
- [ ] Copy full implementation from THREE_EXPERT_REVIEW_AND_PRD.md (lines 600-700)
- [ ] Verify prompt includes:
  - Base recipe title, description, difficulty, servings, cooking time
  - Core ingredients from original
  - Requirements to MAINTAIN consistency
  - Equipment handling (optional use)
  - JSON format with CRITICAL CONSTRAINTS

**Step 2.3c: Keep old method for backward compatibility**
- [ ] Keep existing `generateRecipeDetail(title:pantryContext:cuisines:equipmentOwned:)` method
- [ ] Mark it as deprecated with comment: `// DEPRECATED: Use generateRecipeDetail(baseRecipe:) instead`
- [ ] This ensures old code still works during transition

- [ ] Compile and verify no errors

---

#### Task 2.4: Update GeneratedRecipeDetailView (45 min)
**File:** `Features/RecipeGenerator/GeneratedRecipeDetailView.swift`
**Method:** `fetchRecipeDetail()`

- [ ] Find the section that calls `geminiService.generateRecipeDetail()`
- [ ] Replace with conditional logic:
  ```swift
  let detail: RecipeDetail

  if let baseRecipe = recipeUIModel.baseRecipe {
      // NEW: Use baseRecipe for grounded generation
      detail = try await geminiService.generateRecipeDetail(
          baseRecipe: baseRecipe,
          pantryContext: nil,
          equipmentOwned: authService.userPreferences?.equipmentOwned ?? []
      )
  } else {
      // FALLBACK: Use old method for backward compatibility
      detail = try await geminiService.generateRecipeDetail(
          title: recipeUIModel.title,
          pantryContext: nil,
          cuisines: recipeUIModel.cuisine.map { [$0] } ?? [],
          equipmentOwned: authService.userPreferences?.equipmentOwned ?? []
      )
  }
  ```
- [ ] Compile and verify no errors

---

#### Task 2.5: Update Prefetch Logic (45 min)
**File:** `Services/RecipeServiceAdapter.swift`
**Struct:** `DefaultRecipeDetailPrefetcher`
**Method:** `prefetchDetails(for:pantryIngredients:userPreferences:)`

- [ ] Find the section that calls `GeminiAPIService().generateRecipeDetail()`
- [ ] Add `let baseRecipe = model.baseRecipe` before the call
- [ ] Replace with conditional logic:
  ```swift
  let detail: RecipeDetail

  if let baseRecipe = baseRecipe {
      detail = try await GeminiAPIService().generateRecipeDetail(
          baseRecipe: baseRecipe,
          pantryContext: nil,
          equipmentOwned: equipmentOwned
      )
  } else {
      detail = try await GeminiAPIService().generateRecipeDetail(
          title: title,
          pantryContext: nil,
          cuisines: cuisine.map { [$0] } ?? [],
          equipmentOwned: equipmentOwned
      )
  }
  ```
- [ ] Compile and verify no errors

---

#### Task 2.6: Testing Phase 2 (2 hours)

**Test 2.6a: Recipe Consistency**
- [ ] Generate recipe ideas
- [ ] Note servings, difficulty, cooking time for first recipe
- [ ] Tap to view detail
- [ ] Verify detail matches:
  - Servings: exact match
  - Difficulty: exact match
  - Cooking time: within ±10%
  - Ingredients: superset with measurements

**Test 2.6b: Ingredient Expansion**
- [ ] Generate recipe with ingredients: ["chicken", "rice", "broccoli"]
- [ ] View detail
- [ ] Verify detail ingredients include measurements:
  - "2 lbs chicken breast" (not just "chicken")
  - "2 cups rice" (not just "rice")
  - "1 head broccoli" (not just "broccoli")

**Test 2.6c: Backward Compatibility**
- [ ] Create RecipeUIModel without baseRecipe (set to nil)
- [ ] View detail
- [ ] Verify it falls back to old method gracefully
- [ ] No crashes or errors

**Test 2.6d: Cache Performance**
- [ ] Generate 10 recipes
- [ ] View details for all 10
- [ ] Close and reopen app
- [ ] View same details again
- [ ] Measure cache hit rate (should be >70%)

**Test 2.6e: Performance**
- [ ] Generate recipe and view detail
- [ ] Measure time from tap to detail display
- [ ] Should be <3 seconds

---

### PHASE 3: CLEANUP (1 hour)

#### Task 3.1: Remove Unused Code (15 min)
**File:** `Services/RecipeGenerationService.swift`

- [ ] Delete method: `createRecipePrompt(from: [String])` (lines ~156-182)
- [ ] Delete method: `generateRecipes(from: [String])` (lines ~7-45)
- [ ] Search codebase for any references to these methods
- [ ] Verify no references found
- [ ] Compile and verify no errors

---

#### Task 3.2: Update Documentation (30 min)

- [ ] **File:** `Prompts/ALL_PROMPTS.md`
  - Mark "Recipe ideas – simple" as DEPRECATED
  - Add new section for "Recipe Ideas – Enhanced" with updated prompt
  - Add new section for "Recipe Detail – Grounded" with new prompt

- [ ] **File:** `v12/RECIPE_DETAIL_GROUNDING_PLAN.md`
  - Update status from "Proposed" to "Implemented"
  - Add implementation date
  - Add link to THREE_EXPERT_REVIEW_AND_PRD.md

- [ ] **File:** `README.md` (project root, if applicable)
  - Update recipe generation flow description
  - Mention consistency improvements

---

#### Task 3.3: Final Testing (15 min)

- [ ] Run full test suite
- [ ] Verify all tests pass
- [ ] Run regression tests on other features
- [ ] Verify no unintended side effects
- [ ] Test on multiple devices/simulators

---

## ✅ Completion Checklist

### Phase 1 Complete
- [ ] All Phase 1 tasks completed
- [ ] All Phase 1 tests passed
- [ ] Code reviewed and approved
- [ ] Deployed to staging
- [ ] Stakeholder sign-off

### Phase 2 Complete
- [ ] All Phase 2 tasks completed
- [ ] All Phase 2 tests passed
- [ ] Code reviewed and approved
- [ ] Deployed to beta (10% traffic)
- [ ] Metrics monitored (consistency, cache, performance)
- [ ] Deployed to 50% traffic
- [ ] Deployed to 100% traffic
- [ ] Stakeholder sign-off

### Phase 3 Complete
- [ ] All Phase 3 tasks completed
- [ ] All Phase 3 tests passed
- [ ] Documentation updated
- [ ] Code reviewed and approved
- [ ] Final deployment
- [ ] Stakeholder sign-off

---

## 📊 Success Metrics Tracking

### Consistency Metrics
- [ ] Servings match rate: ___% (target: 100%)
- [ ] Difficulty match rate: ___% (target: 100%)
- [ ] Time match rate (±10%): ___% (target: 95%+)
- [ ] Ingredients superset rate: ___% (target: 100%)

### Performance Metrics
- [ ] Cache hit rate: ___% (target: >70%)
- [ ] API response time: ___s (target: <3s)
- [ ] Token usage increase: ___% (target: <10%)

### User Satisfaction
- [ ] Recipe quality rating: ___ stars (target: 4.5+)
- [ ] User feedback: ___ (positive/negative/neutral)

---

## 🚨 Rollback Plan

If critical issues arise:

**Phase 1 Rollback:**
- Revert prompt changes in RecipeGenerationService.swift
- Revert RecipePreferences changes in Recipe.swift
- Deploy previous version

**Phase 2 Rollback:**
- Keep old `generateRecipeDetail(title:...)` method active
- Update call sites to use old method
- Remove baseRecipe from RecipeUIModel (optional field, safe to ignore)
- Deploy previous version

**Phase 3 Rollback:**
- Restore deleted methods from git history
- Revert documentation changes
- Deploy previous version

---

## 📝 Implementation Notes

**Checklist Owner:** _[Name]_
**Start Date:** _[Date]_
**Target Completion:** _[Date + 5 days]_
**Actual Completion:** _[Date]_

**Notes & Observations:**
Use this section to track issues, blockers, or important observations during implementation:

-
-
-

---

## ✅ Next Steps

1. **Obtain Approvals:**
   - [ ] Engineering Lead approval
   - [ ] Product Manager approval
   - [ ] QA Lead approval

2. **Schedule Implementation:**
   - [ ] Assign implementation owner
   - [ ] Set start date
   - [ ] Set target completion date
   - [ ] Allocate resources

3. **Begin Phase 1:**
   - [ ] Follow the checklist above
   - [ ] Track progress
   - [ ] Report issues

---

**Document Maintained By:** Engineering Team
**Last Updated:** 2025-09-30
**Next Review:** After Phase 1 completion

---

## 📧 Contact

For questions or issues related to this implementation:

- **Technical Questions:** Engineering Lead
- **Product Questions:** Product Manager
- **Testing Questions:** QA Lead
- **Documentation Issues:** Create an issue in the project tracker

---

**Ready to start? Scroll up to [Detailed Implementation Checklist](#-detailed-implementation-checklist) →**

