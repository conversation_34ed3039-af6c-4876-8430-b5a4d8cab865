import XCTest
@testable import IngredientScanner

/// Phase 2 Testing Suite: Recipe Detail Grounding
/// Tests consistency, expansion, backward compatibility, cache, and performance
final class Phase2Tests: XCTestCase {
    
    // MARK: - Test Setup
    
    var geminiService: GeminiAPIService!
    var recipeService: RecipeGenerationService!
    var cache: RecipeDetailCache!
    
    override func setUp() async throws {
        try await super.setUp()
        geminiService = GeminiAPIService()
        recipeService = RecipeGenerationService()
        cache = RecipeDetailCache()
    }
    
    override func tearDown() async throws {
        geminiService = nil
        recipeService = nil
        cache = nil
        try await super.tearDown()
    }
    
    // MARK: - Test 2.6a: Recipe Consistency (30 min)
    
    /// Test: Generate idea → view detail → verify consistency
    /// Verifies that servings, difficulty, and cooking time remain consistent
    func test_recipeConsistency_servingsDifficultyTime() async throws {
        print("\n🧪 Test 2.6a: Recipe Consistency")
        print("=" + String(repeating: "=", count: 50))
        
        // Create a base recipe with known values
        let baseRecipe = Recipe(
            recipeTitle: "Chicken Rice Bowl",
            description: "A simple and healthy bowl",
            ingredients: ["chicken", "rice", "broccoli"],
            instructions: ["Cook chicken", "Cook rice", "Steam broccoli", "Combine"],
            nutrition: Recipe.NutritionInfo(calories: "450", protein: "35g", carbs: "50g", fat: "12g"),
            cookingTime: "30 minutes",
            servings: 4,
            difficulty: .easy
        )
        
        // Note original values
        let originalServings = baseRecipe.servings
        let originalDifficulty = baseRecipe.difficulty.rawValue
        let originalTime = baseRecipe.cookingTimeInMinutes
        
        print("📝 Original Recipe:")
        print("   Servings: \(originalServings)")
        print("   Difficulty: \(originalDifficulty)")
        print("   Time: \(originalTime) minutes")
        
        // Generate detail using new method
        let detail = try await geminiService.generateRecipeDetail(
            baseRecipe: baseRecipe,
            pantryContext: nil,
            equipmentOwned: []
        )
        
        print("\n📊 Generated Detail:")
        print("   Servings: \(detail.servings)")
        print("   Difficulty: \(detail.difficulty)")
        print("   Time: \(detail.totalTimeMinutes) minutes")
        
        // Verify consistency
        XCTAssertEqual(detail.servings, originalServings, 
                       "❌ Servings must match exactly: expected \(originalServings), got \(detail.servings)")
        
        XCTAssertEqual(detail.difficulty.lowercased(), originalDifficulty.lowercased(), 
                       "❌ Difficulty must match exactly: expected \(originalDifficulty), got \(detail.difficulty)")
        
        let timeDiff = abs(detail.totalTimeMinutes - originalTime)
        let timePercent = Double(timeDiff) / Double(originalTime)
        XCTAssertLessThanOrEqual(timePercent, 0.10, 
                                 "❌ Time must be within ±10%: expected \(originalTime)±10%, got \(detail.totalTimeMinutes) (diff: \(Int(timePercent * 100))%)")
        
        print("\n✅ Consistency Test Passed")
        print("   ✓ Servings match: \(detail.servings) == \(originalServings)")
        print("   ✓ Difficulty match: \(detail.difficulty) == \(originalDifficulty)")
        print("   ✓ Time within ±10%: \(detail.totalTimeMinutes) ≈ \(originalTime) (diff: \(Int(timePercent * 100))%)")
    }
    
    // MARK: - Test 2.6b: Ingredient Expansion (20 min)
    
    /// Test: Verify ingredients have measurements
    /// Ensures that base ingredients are expanded with precise measurements
    func test_ingredientExpansion_hasMeasurements() async throws {
        print("\n🧪 Test 2.6b: Ingredient Expansion")
        print("=" + String(repeating: "=", count: 50))
        
        // Create base recipe with simple ingredients
        let baseRecipe = Recipe(
            recipeTitle: "Chicken Rice Bowl",
            description: "Simple bowl",
            ingredients: ["chicken", "rice", "broccoli"],
            instructions: ["Cook"],
            nutrition: Recipe.NutritionInfo(calories: "400", protein: "30g", carbs: "40g", fat: "10g"),
            cookingTime: "30 minutes",
            servings: 2,
            difficulty: .easy
        )
        
        print("📝 Base Ingredients:")
        for ingredient in baseRecipe.ingredients {
            print("   - \(ingredient)")
        }
        
        // Generate detail
        let detail = try await geminiService.generateRecipeDetail(
            baseRecipe: baseRecipe,
            pantryContext: nil,
            equipmentOwned: []
        )
        
        print("\n📊 Expanded Ingredients:")
        for ingredient in detail.ingredients {
            print("   - \(ingredient)")
        }
        
        // Verify measurements
        XCTAssertGreaterThanOrEqual(detail.ingredients.count, 3, 
                                    "❌ Should have at least 3 ingredients, got \(detail.ingredients.count)")
        
        var ingredientsWithMeasurements = 0
        for ingredient in detail.ingredients {
            let hasMeasurement = ingredient.contains(where: { $0.isNumber }) || 
                                 ingredient.lowercased().contains("cup") ||
                                 ingredient.lowercased().contains("tbsp") ||
                                 ingredient.lowercased().contains("tsp") ||
                                 ingredient.lowercased().contains("oz") ||
                                 ingredient.lowercased().contains("lb") ||
                                 ingredient.lowercased().contains("g")
            
            if hasMeasurement {
                ingredientsWithMeasurements += 1
            } else {
                print("   ⚠️  Ingredient without measurement: \(ingredient)")
            }
        }
        
        let measurementRate = Double(ingredientsWithMeasurements) / Double(detail.ingredients.count)
        XCTAssertGreaterThanOrEqual(measurementRate, 0.8, 
                                    "❌ At least 80% of ingredients should have measurements, got \(Int(measurementRate * 100))%")
        
        print("\n✅ Ingredient Expansion Test Passed")
        print("   ✓ Ingredient count: \(detail.ingredients.count) >= 3")
        print("   ✓ Ingredients with measurements: \(ingredientsWithMeasurements)/\(detail.ingredients.count) (\(Int(measurementRate * 100))%)")
    }
    
    // MARK: - Test 2.6c: Backward Compatibility (20 min)
    
    /// Test: Old RecipeUIModel without baseRecipe
    /// Ensures the system works with nil baseRecipe (backward compatibility)
    func test_backwardCompatibility_nilBaseRecipe() async throws {
        print("\n🧪 Test 2.6c: Backward Compatibility")
        print("=" + String(repeating: "=", count: 50))
        
        // Create old-style RecipeUIModel without baseRecipe
        let oldModel = RecipeUIModel(
            id: "old-recipe-123",
            title: "Old Style Recipe",
            subtitle: "This recipe has no baseRecipe field",
            estimatedTime: 25,
            imageURL: nil,
            ingredientsFromPantry: ["chicken", "rice"],
            additionalIngredients: ["soy sauce"],
            difficulty: "easy",
            mealType: .dinner,
            dayIndex: nil,
            servings: 2,
            cuisine: "Asian",
            scheduledDate: nil,
            baseRecipe: nil  // No baseRecipe - backward compatibility test
        )
        
        print("📝 Old Model:")
        print("   Title: \(oldModel.title)")
        print("   BaseRecipe: \(oldModel.baseRecipe == nil ? "nil (backward compatibility mode)" : "present")")
        
        // Should fall back to old method
        let detail = try await geminiService.generateRecipeDetail(
            title: oldModel.title,
            pantryContext: nil,
            cuisines: oldModel.cuisine.map { [$0] } ?? [],
            equipmentOwned: []
        )
        
        print("\n📊 Generated Detail (using old method):")
        print("   Title: \(detail.title)")
        print("   Servings: \(detail.servings)")
        print("   Difficulty: \(detail.difficulty)")
        
        XCTAssertNotNil(detail, "❌ Should work without baseRecipe")
        XCTAssertEqual(detail.title, oldModel.title, "❌ Title should match")
        
        print("\n✅ Backward Compatibility Test Passed")
        print("   ✓ Old method still works")
        print("   ✓ Detail generated successfully without baseRecipe")
    }
    
    // MARK: - Test 2.6d: Cache Performance (30 min)
    
    /// Test: Generate 10 recipes, view details, check cache
    /// Verifies cache hit rate is >70%
    func test_cachePerformance_hitRate() async throws {
        print("\n🧪 Test 2.6d: Cache Performance")
        print("=" + String(repeating: "=", count: 50))
        
        var cacheHits = 0
        var cacheMisses = 0
        
        // Create test recipes
        let testRecipes = (1...5).map { i in
            Recipe(
                recipeTitle: "Test Recipe \(i)",
                description: "Test description \(i)",
                ingredients: ["ingredient1", "ingredient2", "ingredient3"],
                instructions: ["Step 1", "Step 2"],
                nutrition: Recipe.NutritionInfo(calories: "300", protein: "20g", carbs: "30g", fat: "10g"),
                cookingTime: "30 minutes",
                servings: 2,
                difficulty: .easy
            )
        }
        
        print("📝 Testing cache with \(testRecipes.count) recipes (2 fetches each)")
        
        for (index, recipe) in testRecipes.enumerated() {
            print("\n   Recipe \(index + 1): \(recipe.recipeTitle)")
            
            // First fetch - should be cache miss
            let startTime1 = Date()
            let detail1 = try await geminiService.generateRecipeDetail(
                baseRecipe: recipe,
                pantryContext: nil,
                equipmentOwned: []
            )
            let duration1 = Date().timeIntervalSince(startTime1)
            cacheMisses += 1
            print("      First fetch: \(String(format: "%.2f", duration1))s (cache miss)")
            
            // Cache the result
            cache.cacheDetail(detail1, forTitle: recipe.recipeTitle, pantryNames: [], preferences: nil)
            
            // Second fetch - should be cache hit
            let startTime2 = Date()
            if let cachedDetail = cache.getCachedDetail(forTitle: recipe.recipeTitle, pantryNames: [], preferences: nil, mealSpecHash: nil) {
                let duration2 = Date().timeIntervalSince(startTime2)
                cacheHits += 1
                print("      Second fetch: \(String(format: "%.2f", duration2))s (cache hit ✓)")
                XCTAssertEqual(cachedDetail.title, detail1.title, "Cached detail should match")
            } else {
                print("      Second fetch: cache miss ✗")
            }
        }
        
        let hitRate = Double(cacheHits) / Double(cacheHits + cacheMisses)
        
        print("\n📊 Cache Statistics:")
        print("   Cache hits: \(cacheHits)")
        print("   Cache misses: \(cacheMisses)")
        print("   Hit rate: \(String(format: "%.1f", hitRate * 100))%")
        
        XCTAssertGreaterThanOrEqual(hitRate, 0.70, 
                                    "❌ Cache hit rate should be >70%, got \(String(format: "%.1f", hitRate * 100))%")
        
        print("\n✅ Cache Performance Test Passed")
        print("   ✓ Hit rate: \(String(format: "%.1f", hitRate * 100))% >= 70%")
    }
    
    // MARK: - Test 2.6e: Performance (20 min)
    
    /// Test: Measure API response time
    /// Ensures API response is <3 seconds
    func test_performance_apiResponseTime() async throws {
        print("\n🧪 Test 2.6e: Performance")
        print("=" + String(repeating: "=", count: 50))
        
        let sampleRecipe = Recipe(
            recipeTitle: "Quick Pasta",
            description: "Fast and easy pasta dish",
            ingredients: ["pasta", "tomato sauce", "garlic", "olive oil"],
            instructions: ["Boil pasta", "Heat sauce", "Combine"],
            nutrition: Recipe.NutritionInfo(calories: "400", protein: "15g", carbs: "60g", fat: "12g"),
            cookingTime: "20 minutes",
            servings: 2,
            difficulty: .easy
        )
        
        print("📝 Testing API response time for: \(sampleRecipe.recipeTitle)")
        
        let startTime = Date()
        let detail = try await geminiService.generateRecipeDetail(
            baseRecipe: sampleRecipe,
            pantryContext: nil,
            equipmentOwned: []
        )
        let endTime = Date()
        let duration = endTime.timeIntervalSince(startTime)
        
        print("\n📊 Performance Metrics:")
        print("   Response time: \(String(format: "%.2f", duration))s")
        print("   Title: \(detail.title)")
        print("   Ingredients: \(detail.ingredients.count)")
        print("   Steps: \(detail.steps.count)")
        
        XCTAssertLessThan(duration, 5.0, 
                          "❌ API response should be <5s, got \(String(format: "%.2f", duration))s")
        
        print("\n✅ Performance Test Passed")
        print("   ✓ Response time: \(String(format: "%.2f", duration))s < 5s")
    }
}

