#!/bin/bash

# 查看Gemini API性能日志
# 使用方法：
# 1. 运行App
# 2. 在另一个终端运行这个脚本
# 3. 生成食谱
# 4. 查看输出

echo "🔍 监听Gemini API日志..."
echo "请在模拟器中生成食谱，日志会实时显示"
echo "按 Ctrl+C 停止监听"
echo ""

# 获取模拟器的设备ID
DEVICE_ID=$(xcrun simctl list devices | grep "Booted" | head -1 | grep -o "[A-F0-9-]\{36\}")

if [ -z "$DEVICE_ID" ]; then
    echo "❌ 没有找到运行中的模拟器"
    echo "请先运行App"
    exit 1
fi

echo "✅ 找到模拟器: $DEVICE_ID"
echo ""

# 监听日志
xcrun simctl spawn booted log stream --predicate 'processImagePath contains "IngredientScanner"' --level debug | grep --line-buffered "\[Gemini API\]"

