# Performance Fix Implementation Guide

**Issue:** Recipe generation taking 10+ seconds  
**Root Cause:** No `maxOutputTokens` limit in GeminiAPIService  
**Solution:** Add generation config with token limits  
**Expected Result:** 70% speed improvement (10s → 3s)

---

## 🚀 Quick Fix (10 minutes)

### Step 1: Update GeminiAPIService.callGeminiAPI

**File:** `Services/GeminiAPIService.swift`  
**Lines:** 183-196

**BEFORE:**
```swift
func callGeminiAPI(prompt: String) async throws -> String {
    guard apiKey != "YOUR_GEMINI_API_KEY_HERE" && !apiKey.isEmpty else {
        throw GeminiError.apiKeyNotConfigured("Gemini API key not configured. Please update APIKeys.swift")
    }

    let requestBody: [String: Any] = [
        "contents": [
            [
                "parts": [
                    ["text": prompt]
                ]
            ]
        ]
    ]
    // ... rest of method
}
```

**AFTER:**
```swift
func callGeminiAPI(prompt: String, maxTokens: Int = 1024) async throws -> String {
    guard apiKey != "YOUR_GEMINI_API_KEY_HERE" && !apiKey.isEmpty else {
        throw GeminiError.apiKeyNotConfigured("Gemini API key not configured. Please update APIKeys.swift")
    }

    let requestBody: [String: Any] = [
        "contents": [
            [
                "parts": [
                    ["text": prompt]
                ]
            ]
        ],
        "generationConfig": [
            "temperature": 0.7,
            "topP": 0.95,
            "maxOutputTokens": maxTokens,
            "response_mime_type": "application/json"
        ]
    ]
    // ... rest of method
}
```

---

### Step 2: Update Call Sites with Appropriate Token Limits

#### 2a. Ingredient Canonicalization (Scan)
**File:** `Services/GeminiAPIService.swift`  
**Line:** 31

**BEFORE:**
```swift
let response = try await callGeminiAPI(prompt: prompt)
```

**AFTER:**
```swift
let response = try await callGeminiAPI(prompt: prompt, maxTokens: 256)
```

---

#### 2b. Custom Ingredients Canonicalization
**File:** `Services/GeminiAPIService.swift`  
**Line:** 98

**BEFORE:**
```swift
let response = try await callGeminiAPI(prompt: prompt)
```

**AFTER:**
```swift
let response = try await callGeminiAPI(prompt: prompt, maxTokens: 256)
```

---

#### 2c. Recipe Detail Generation (Base Recipe)
**File:** `Services/GeminiAPIService.swift`  
**Line:** 327

**BEFORE:**
```swift
let response = try await callGeminiAPI(prompt: prompt)
```

**AFTER:**
```swift
let response = try await callGeminiAPI(prompt: prompt, maxTokens: 1024)
```

---

#### 2d. Recipe Detail Generation (Legacy)
**File:** `Services/GeminiAPIService.swift`  
**Line:** 452

**BEFORE:**
```swift
let response = try await callGeminiAPI(prompt: prompt)
```

**AFTER:**
```swift
let response = try await callGeminiAPI(prompt: prompt, maxTokens: 1024)
```

---

### Step 3: Update RecipeGenerationService Token Limit

**File:** `Services/RecipeGenerationService.swift`  
**Lines:** 75-91

**BEFORE:**
```swift
private func processRecipeText(_ prompt: String) async throws -> String {
    // Create a simple request to Gemini API for recipe generation
    let requestBody: [String: Any] = [
        "contents": [
            [
                "parts": [
                    ["text": prompt]
                ]
            ]
        ],
        "generationConfig": [
            "temperature": 0.7,
            "topP": 0.95,
            "maxOutputTokens": 2048,  // ❌ Too high!
            "response_mime_type": "application/json"
        ]
    ]
    // ... rest of method
}
```

**AFTER:**
```swift
private func processRecipeText(_ prompt: String, dishCount: Int = 3) async throws -> String {
    // Calculate optimal token limit based on dish count
    // Each recipe ~150-200 tokens, add overhead
    let maxTokens = dishCount * 200 + 100
    
    // Create a simple request to Gemini API for recipe generation
    let requestBody: [String: Any] = [
        "contents": [
            [
                "parts": [
                    ["text": prompt]
                ]
            ]
        ],
        "generationConfig": [
            "temperature": 0.7,
            "topP": 0.95,
            "maxOutputTokens": maxTokens,  // ✅ Dynamic based on dish count
            "response_mime_type": "application/json"
        ]
    ]
    // ... rest of method
}
```

---

### Step 4: Update generateMealIdeas to Pass Dish Count

**File:** `Services/RecipeGenerationService.swift`  
**Lines:** 27-30

**BEFORE:**
```swift
func generateMealIdeas(from ingredients: [String], preferences: RecipePreferences) async throws -> [RecipeIdea] {
    // Build prompt with preferences
    let prompt = createRecipePrompt(from: ingredients, preferences: preferences)
    let jsonResponse = try await processRecipeText(prompt)
    // ... rest of method
}
```

**AFTER:**
```swift
func generateMealIdeas(from ingredients: [String], preferences: RecipePreferences) async throws -> [RecipeIdea] {
    // Build prompt with preferences
    let prompt = createRecipePrompt(from: ingredients, preferences: preferences)
    let dishCount = max(1, min(12, preferences.targetDishCount ?? 3))
    let jsonResponse = try await processRecipeText(prompt, dishCount: dishCount)
    // ... rest of method
}
```

---

## 📊 Expected Performance Improvements

### Token Limit Comparison

| Use Case | Before | After | Improvement |
|----------|--------|-------|-------------|
| **Recipe Ideas (2 recipes)** | 2048 tokens | 500 tokens | 75% reduction |
| **Recipe Ideas (3 recipes)** | 2048 tokens | 700 tokens | 66% reduction |
| **Recipe Detail** | No limit (~8192) | 1024 tokens | 87% reduction |
| **Ingredient Scan** | No limit (~8192) | 256 tokens | 97% reduction |

### Time Improvement Estimates

| Scenario | Before | After | Improvement |
|----------|--------|-------|-------------|
| **2 recipes** | ~10s | ~2.5-3s | 70% faster ✅ |
| **3 recipes** | ~15s | ~3.5-4s | 75% faster ✅ |
| **Recipe detail** | ~5s | ~2s | 60% faster ✅ |
| **Ingredient scan** | ~3s | ~1s | 67% faster ✅ |

---

## ✅ Testing Checklist

After implementing the changes:

- [ ] **Test 1:** Generate 2 recipes
  - Expected: <3 seconds
  - Verify: All recipe fields present and complete

- [ ] **Test 2:** Generate 3 recipes
  - Expected: <4 seconds
  - Verify: All recipe fields present and complete

- [ ] **Test 3:** View recipe detail
  - Expected: <3 seconds
  - Verify: Detailed steps and measurements present

- [ ] **Test 4:** Scan ingredients
  - Expected: <2 seconds
  - Verify: Ingredients correctly categorized

- [ ] **Test 5:** Add custom ingredients
  - Expected: <2 seconds
  - Verify: Ingredients correctly categorized

---

## 🔍 Verification

### How to Measure Performance

Add timing logs to measure actual performance:

```swift
// In RecipeGenerationService.generateMealIdeas
let startTime = Date()
let jsonResponse = try await processRecipeText(prompt, dishCount: dishCount)
let duration = Date().timeIntervalSince(startTime)
print("⏱️ Recipe generation took \(String(format: "%.2f", duration))s")
```

### Success Criteria

✅ **Pass:** 2-recipe generation <3 seconds  
✅ **Pass:** 3-recipe generation <4 seconds  
✅ **Pass:** Recipe detail <3 seconds  
✅ **Pass:** No truncated or incomplete responses

---

## 🚨 Troubleshooting

### Issue: Responses are truncated

**Symptom:** Recipes missing fields or incomplete JSON

**Solution:** Increase token limit slightly
```swift
// Increase by 20%
let maxTokens = Int(Double(dishCount * 200 + 100) * 1.2)
```

---

### Issue: Still slow after changes

**Check:**
1. Verify `generationConfig` is in request body
2. Check network connection speed
3. Verify Gemini API status
4. Check prompt length (should be <600 tokens)

---

### Issue: Quality degradation

**Symptom:** Recipes are less detailed or creative

**Solution:** Adjust temperature or increase token limit slightly
```swift
"temperature": 0.8,  // More creative (was 0.7)
"maxOutputTokens": dishCount * 250 + 100  // 25% more tokens
```

---

## 📝 Summary

**Changes Made:**
1. ✅ Added `maxTokens` parameter to `callGeminiAPI`
2. ✅ Added `generationConfig` to request body
3. ✅ Updated all call sites with appropriate token limits
4. ✅ Made RecipeGenerationService token limit dynamic

**Files Modified:**
- `Services/GeminiAPIService.swift` (5 locations)
- `Services/RecipeGenerationService.swift` (2 locations)

**Total Time:** 10-15 minutes  
**Expected Improvement:** 70% faster (10s → 3s)  
**Risk:** Very Low  
**Rollback:** Easy (just revert changes)

---

**Ready to implement? Let's do it!** 🚀

