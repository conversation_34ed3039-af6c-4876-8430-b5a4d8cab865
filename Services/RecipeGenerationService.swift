import Foundation
import UIKit

actor RecipeGenerationService: RecipeGenerationServiceProtocol {
    private let geminiService = GeminiAPIService()
    
    // REMOVED: generateRecipes(from: [String]) - unused legacy method
    // This method was replaced by generateMealIdeas(from:preferences:) in V12
    // Deleted as part of Phase 3 cleanup (2025-09-30)

    // MARK: - V6 Structured Plan API Convenience
    /// Generate a structured MealPlan using slot-based assignment.
    /// This delegates to StructuredMealPlanGenerator and the existing adapter for per-meal batches.
    func generateStructuredMealPlan(_ planRequest: MealPlanGenerationRequest,
                                    pantryService: PantryService,
                                    authService: AuthenticationService) async throws -> MealPlan {
        let adapter = RecipeServiceAdapter(recipeService: self, pantryService: pantryService)
        let generator = StructuredMealPlanGenerator(
            adapter: adapter,
            pantryService: pantryService,
            authService: authService,
            cutoffManager: MealCutoffManager()
        )
        return try await generator.generatePlan(planRequest)
    }
    
    func generateMealIdeas(from ingredients: [String], preferences: RecipePreferences) async throws -> [RecipeIdea] {
        // Build prompt with preferences
        let prompt = createRecipePrompt(from: ingredients, preferences: preferences)
        let dishCount = max(1, min(12, preferences.targetDishCount ?? 3))
        let jsonResponse = try await processRecipeText(prompt, dishCount: dishCount)
        guard let jsonData = extractJSON(from: jsonResponse) else {
            throw RecipeGenerationError.invalidJSONResponse
        }
        let recipeData = try JSONDecoder().decode([RecipeData].self, from: jsonData)
        var recipes = recipeData.map { data in
            Recipe(
                recipeTitle: data.title,
                description: data.description,
                ingredients: data.ingredients,
                instructions: data.instructions,
                nutrition: Recipe.NutritionInfo(
                    calories: data.nutrition.calories,
                    protein: data.nutrition.protein,
                    carbs: data.nutrition.carbs,
                    fat: data.nutrition.fat
                ),
                cookingTime: data.cookingTime,
                servings: data.servings,
                difficulty: Recipe.Difficulty(rawValue: data.difficulty) ?? .medium
            )
        }
        
        // Safety post-filter by preferences
        if preferences.respectRestrictions {
            let blocked = Set((preferences.allergiesAndIntolerances +
                                preferences.strictExclusions +
                                preferences.customStrictExclusions).map { $0.lowercased() })
            recipes = recipes.filter { recipe in
                let ing = Set(recipe.ingredients.map { $0.lowercased() })
                return blocked.isDisjoint(with: ing)
            }
        }
        
        return recipes.map { recipe in
            RecipeIdea(
                recipe: recipe,
                status: .readyToCook,
                missingIngredients: []
            )
        }
    }
    
    // MARK: - Private Helper Methods

    private func processRecipeText(_ prompt: String, dishCount: Int = 3) async throws -> String {
        // Calculate optimal token limit based on dish count
        // Each recipe ~150-200 tokens, add overhead
        let maxTokens = dishCount * 200 + 100

        // Use GeminiAPIService for consistent API calls with proper configuration
        do {
            return try await geminiService.callGeminiAPI(prompt: prompt, maxTokens: maxTokens)
        } catch {
            // Convert GeminiError to RecipeGenerationError for consistency
            if let geminiError = error as? GeminiAPIService.GeminiError {
                switch geminiError {
                case .apiKeyNotConfigured:
                    throw RecipeGenerationError.invalidRequest
                case .invalidResponseWithMessage, .parsingErrorWithMessage:
                    throw RecipeGenerationError.invalidResponse
                default:
                    throw RecipeGenerationError.invalidResponse
                }
            }
            throw error
        }
    }
    
    // REMOVED: createRecipePrompt(from: [String]) - unused legacy method
    // This method was replaced by createRecipePrompt(from:preferences:) in V12
    // Deleted as part of Phase 3 cleanup (2025-09-30)
    
    private func createRecipePrompt(from ingredients: [String], preferences: RecipePreferences) -> String {
        let dishTarget = max(1, min(12, preferences.targetDishCount ?? 3))

        var constraints: [String] = []
        constraints.append("Return at least \(dishTarget) distinct recipes; we will trim to \(dishTarget) if extras are provided.")

        if let mealType = preferences.targetMealType {
            constraints.append("All recipes must be suitable for \(mealType.rawValue) meals.")
        }
        if !preferences.cuisines.isEmpty {
            constraints.append("Cuisine suggestions (not strict requirements): \(preferences.cuisines.joined(separator: ", ")). You may use one, mix multiple, create fusion dishes, or draw inspiration from these styles.")
        }
        if let additional = preferences.additionalRequest?.trimmingCharacters(in: .whitespacesAndNewlines), !additional.isEmpty {
            constraints.append("Additional request: \(additional).")
        }
        if !preferences.dietaryRestrictions.isEmpty {
            constraints.append("Dietary preferences: \(preferences.dietaryRestrictions.joined(separator: ", ")).")
        }
        if preferences.respectRestrictions {
            if !preferences.allergiesAndIntolerances.isEmpty {
                constraints.append("Allergies/Intolerances: strictly avoid \(preferences.allergiesAndIntolerances.joined(separator: ", ")).")
            }
            let strictItems = (preferences.strictExclusions + preferences.customStrictExclusions)
            if !strictItems.isEmpty {
                constraints.append("Do NOT include: \(strictItems.joined(separator: ", ")).")
            }
        }
        if !preferences.equipmentOwned.isEmpty {
            constraints.append("Special equipment available (optional to use): \(preferences.equipmentOwned.joined(separator: ", ")). You may also use basic kitchen equipment (microwave, oven, stovetop).")
        } else {
            constraints.append("Assume basic kitchen equipment is available (microwave, oven, stovetop).")
        }
        // V12: Kid-friendly constraint
        if preferences.numberOfKids > 0 {
            constraints.append("Family includes \(preferences.numberOfKids) kid(s) - make recipes kid-friendly with milder spices, familiar flavors, and simpler textures.")
        }

        let constraintsText: String
        if constraints.isEmpty {
            constraintsText = ""
        } else {
            constraintsText = """

        Constraints:
        - \(constraints.joined(separator: "\n- "))

        """
        }

        return """
        Generate \(dishTarget) recipes. Ingredients: \(ingredients.joined(separator: ", ")). Servings: \(preferences.numberOfServings). Time: ~\(preferences.cookingTimeInMinutes)min.
        \(constraintsText)JSON array format:
        [{"title":"Recipe Name","description":"Brief","ingredients":["item1","item2"],"instructions":["step1","step2"],"cookingTime":"30 minutes","servings":\(preferences.numberOfServings),"difficulty":"easy|medium|hard","nutrition":{"calories":"350","protein":"25g","carbs":"30g","fat":"15g"}}]

        Rules: Use provided ingredients + staples. Avoid allergens. Brief instructions (3-5 steps). No measurements in ingredients.
        """
    }
    
    private func extractJSON(from text: String) -> Data? {
        // Look for JSON array in the response
        guard let startIndex = text.firstIndex(of: "["),
              let endIndex = text.lastIndex(of: "]") else {
            return nil
        }
        
        let jsonString = String(text[startIndex...endIndex])
        return jsonString.data(using: .utf8)
    }
}

// MARK: - Supporting Types

struct RecipeData: Codable {
    let title: String
    let description: String
    let ingredients: [String]
    let instructions: [String]
    let cookingTime: String
    let servings: Int
    let difficulty: String
    let nutrition: NutritionData
}

struct NutritionData: Codable {
    let calories: String
    let protein: String
    let carbs: String
    let fat: String
}

enum RecipeGenerationError: Error, LocalizedError {
    case invalidRequest
    case invalidResponse
    case invalidJSONResponse
    case processingFailed(String)
    case noPantryItems
    
    var errorDescription: String? {
        switch self {
        case .invalidRequest:
            return "Invalid request format"
        case .invalidResponse:
            return "Invalid response from API"
        case .invalidJSONResponse:
            return "Could not parse JSON response"
        case .processingFailed(let message):
            return "Processing failed: \(message)"
        case .noPantryItems:
            return "No pantry items available for recipe generation"
        }
    }
} 
