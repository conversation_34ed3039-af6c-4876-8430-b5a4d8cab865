import Foundation
import UIKit

/// Private utility to detect and filter out non-food items
private struct NonFoodDetector {
    private let nonFoodKeywords: Set<String> = [
        // Cleaning products
        "detergent", "soap", "cleaner", "bleach", "disinfectant", "wipes",
        "cleaning", "scrub", "polish", "degreaser", "sanitizer", "antibacterial",
        // Personal care
        "shampoo", "conditioner", "toothpaste", "deodorant", "lotion",
        "moisturizer", "sunscreen", "perfume", "cologne", "makeup",
        "cosmetic", "skincare", "haircare", "bodywash", "facewash",
        // Household items
        "paper towel", "tissue", "napkin", "foil", "wrap", "bag",
        "plastic bag", "garbage bag", "trash bag", "aluminum foil",
        "parchment paper", "wax paper", "toilet paper",
        // Medications and supplements
        "medicine", "pill", "tablet", "vitamin", "supplement",
        "medication", "drug", "prescription", "antibiotic", "painkiller",
        // Other non-food items
        "battery", "light bulb", "pen", "pencil", "marker", "tape",
        "glue", "scissors", "rubber band", "paper clip", "stapler",
        "charger", "cable", "phone case", "headphones"
    ]

    func isNonFood(_ name: String) -> Bool {
        let lower = name.lowercased()
        return nonFoodKeywords.contains { keyword in
            lower.contains(keyword)
        }
    }
}

actor GeminiAPIService {
    private let apiKey = APIKeys.geminiAPIKey
    // Updated to Gemini 2.5 Flash Lite for faster performance and cost efficiency
    // Reference: https://ai.google.dev/gemini-api/docs/models/gemini#gemini-2.5-flash-lite
    private let baseURL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent"

    // MARK: - Type Aliases for Task 31
    typealias VisionOutput = GoogleVisionAPIService.VisionOutput

    // MARK: - Ingredient Canonicalization (Scan outputs)

    /// Process Vision outputs and return standardized ingredients with strict category enforcement
    func canonicalizeIngredients(
        visionOutputs: [VisionOutput],
        allowedCategories: [String]
    ) async throws -> [Ingredient] {
        // Validate inputs
        guard !visionOutputs.isEmpty else {
            throw GeminiError.invalidInput("Vision outputs cannot be empty")
        }

        guard !allowedCategories.isEmpty else {
            throw GeminiError.invalidInput("Allowed categories cannot be empty")
        }

        // Construct prompt with vision outputs and allowed categories
        let prompt = buildCanonicalizePrompt(visionOutputs: visionOutputs, allowedCategories: allowedCategories)

        // Call Gemini API with token limit for ingredient scanning
        let response = try await callGeminiAPI(prompt: prompt, maxTokens: 256)

        // Parse response into ingredients
        return try parseIngredientsResponse(response, allowedCategories: allowedCategories)
    }

    /// Build a structured prompt for ingredient canonicalization
    private func buildCanonicalizePrompt(visionOutputs: [VisionOutput], allowedCategories: [String]) -> String {
        var prompt = """
        You are a food ingredient analyzer. Process the following OCR text and labels from images of food items, receipts, or packaging.

        CRITICAL RULES:
        1. ONLY process food items. REJECT these non-food categories:
           - Cleaning products (detergent, soap, bleach, disinfectant, wipes, etc.)
           - Personal care (shampoo, conditioner, toothpaste, deodorant, lotion, etc.)
           - Household items (paper towels, tissues, foil, plastic bags, etc.)
           - Medications and supplements (medicine, pills, vitamins, etc.)
           - Other non-food items (batteries, light bulbs, pens, etc.)

        2. For each FOOD item:
           - Extract the ingredient name, removing brand names, sizes, quantities, and marketing text
           - CRITICAL: Normalize to SINGULAR form (e.g., "tomatoes" -> "Tomato", "grapes" -> "Grape", "berries" -> "Berry", "loaves" -> "Loaf")
             Always use singular form unless the ingredient is inherently plural (e.g., "Noodles", "Oats")
           - Preserve meaningful descriptors (e.g., "whole milk" instead of just "milk")
           - Categorize into EXACTLY ONE category: \(allowedCategories.joined(separator: ", "))

        3. Categorization guidelines:
           - Dumplings, wontons, buns → Frozen Foods (if frozen) or Grains, Pasta & Legumes (if fresh)
           - Rice cakes, mochi → Grains, Pasta & Legumes
           - Prepared meals → Frozen Foods or Canned & Broths (based on packaging)
           - When uncertain, prefer specific category over "Other"

        4. If an item is clearly non-food, OMIT it entirely from output

        Return ONLY a JSON array: [{"name":"ingredient name","category":"exact category"}]
        No explanations or additional text.
        """

        // Add vision outputs to prompt
        for (index, output) in visionOutputs.enumerated() {
            prompt += "\n\nIMAGE \(index + 1):\nOCR TEXT: \(output.ocrText)\nLABELS: \(output.labels.joined(separator: ", "))"
        }

        return prompt
    }

    // MARK: - Custom Inputs Canonicalization (Add Sheet)

    /// Clean and categorize free-form custom ingredient inputs using Gemini
    /// - Parameters:
    ///   - names: Free-form custom names typed by the user
    ///   - allowedCategories: Allowed category strings (must map exactly)
    /// - Returns: Ingredients with clamped categories
    func canonicalizeCustomIngredients(
        names: [String],
        allowedCategories: [String]
    ) async throws -> [Ingredient] {
        let trimmed = names.map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }.filter { !$0.isEmpty }
        guard !trimmed.isEmpty else { return [] }
        guard !allowedCategories.isEmpty else {
            throw GeminiError.invalidInput("Allowed categories cannot be empty")
        }

        let joinedList = trimmed.map { "- \($0)" }.joined(separator: "\n")
        let prompt = """
        You are an assistant that standardizes a SHORT list of free-form pantry ingredients typed by a user.

        CRITICAL RULES:
        1. ONLY process food items. REJECT non-food items like:
           - Cleaning products (detergent, soap, bleach, etc.)
           - Personal care (shampoo, toothpaste, lotion, etc.)
           - Household items (paper towels, foil, bags, etc.)
           - Medications (medicine, pills, vitamins, etc.)

        2. For each FOOD item:
           - Output a cleaned ingredient name: remove brands, sizes, quantities, and marketing text
           - CRITICAL: Normalize to SINGULAR form (e.g., "tomatoes" -> "Tomato", "grapes" -> "Grape", "berries" -> "Berry")
             Always use singular form unless the ingredient is inherently plural (e.g., "Noodles", "Oats")
           - Preserve meaningful descriptors like "whole milk", "unsalted butter", "brown rice"
           - Capitalize the first letter of each ingredient name (e.g., "whole milk" -> "Whole milk")
           - Assign EXACTLY ONE category per item. The category MUST be one of: \(allowedCategories.joined(separator: ", "))

        3. If an item is clearly non-food, OMIT it entirely from output

        Return ONLY a JSON array of objects with fields name and category. Example:
        [{"name":"Whole milk","category":"Dairy"}]

        INPUT ITEMS:
        \(joinedList)
        """

        let response = try await callGeminiAPI(prompt: prompt, maxTokens: 256)
        return try parseIngredientsResponse(response, allowedCategories: allowedCategories)
    }

    /// Parse Gemini API response into Ingredient objects with strict validation
    private func parseIngredientsResponse(_ response: String, allowedCategories: [String]) throws -> [Ingredient] {
        // Extract JSON array from response
        guard let jsonData = response.data(using: .utf8) else {
            throw GeminiError.invalidResponseWithMessage("Could not convert response to data")
        }

        // Decode JSON
        struct GeminiIngredient: Decodable {
            let name: String
            let category: String
        }

        let decoder = JSONDecoder()
        let geminiIngredients: [GeminiIngredient]

        do {
            geminiIngredients = try decoder.decode([GeminiIngredient].self, from: jsonData)
        } catch {
            throw GeminiError.parsingErrorWithMessage("Failed to decode JSON response: \(error.localizedDescription)")
        }

        // Convert to Ingredient objects with validation and quality checks
        let canonicalizer = NameCanonicalizer()
        let nonFoodDetector = NonFoodDetector()  // NEW: Add non-food detector
        let validIngredients = geminiIngredients.compactMap { geminiIngredient -> Ingredient? in
            // Trim and canonicalize ingredient name to match pantry formatting rules
            let trimmed = geminiIngredient.name.trimmingCharacters(in: .whitespacesAndNewlines)
            let cleanedName = canonicalizer.canonicalize(trimmed)

            // NEW: Filter non-food items
            guard !nonFoodDetector.isNonFood(cleanedName) else {
                print("⚠️ Filtered non-food item: \(cleanedName)")
                return nil
            }

            // Filter out invalid ingredient names
            guard isValidIngredientName(cleanedName) else {
                return nil
            }

            // Verify category is allowed
            guard allowedCategories.contains(geminiIngredient.category) else {
                return nil
            }

            // Create Ingredient with valid category
            guard let category = PantryCategory(rawValue: geminiIngredient.category) else {
                return nil
            }

            return Ingredient(
                id: UUID(),
                name: cleanedName,
                category: category
            )
        }

        return validIngredients
    }

    /// Validate ingredient name quality
    private func isValidIngredientName(_ name: String) -> Bool {
        // Must have reasonable length
        guard name.count >= 2 && name.count <= 100 else {
            return false
        }

        // Must not be purely numeric
        guard !name.allSatisfy({ $0.isNumber || $0.isWhitespace }) else {
            return false
        }

        // Must not have excessive punctuation
        let punctuationCount = name.filter { $0.isPunctuation }.count
        guard Double(punctuationCount) / Double(name.count) <= 0.5 else {
            return false
        }

        // Must contain at least one letter
        guard name.contains(where: { $0.isLetter }) else {
            return false
        }

        return true
    }

    /// Make API call to Gemini service
    func callGeminiAPI(prompt: String, maxTokens: Int = 1024) async throws -> String {
        guard apiKey != "YOUR_GEMINI_API_KEY_HERE" && !apiKey.isEmpty else {
            throw GeminiError.apiKeyNotConfigured("Gemini API key not configured. Please update APIKeys.swift")
        }

        // Performance logging
        let startTime = Date()
        let promptLength = prompt.count
        print("🚀 [Gemini API] Starting request - Prompt: \(promptLength) chars, MaxTokens: \(maxTokens)")

        let requestBody: [String: Any] = [
            "contents": [
                [
                    "parts": [
                        ["text": prompt]
                    ]
                ]
            ],
            "generationConfig": [
                "temperature": 0.7,
                "topP": 0.95,
                "maxOutputTokens": maxTokens,
                "response_mime_type": "application/json"
            ]
        ]

        do {
            let jsonData = try JSONSerialization.data(withJSONObject: requestBody)

            guard let url = URL(string: "\(baseURL)?key=\(apiKey)") else {
                throw GeminiError.invalidURL
            }

            var request = URLRequest(url: url)
            request.httpMethod = "POST"
            request.setValue("application/json", forHTTPHeaderField: "Content-Type")
            request.httpBody = jsonData
            request.timeoutInterval = 60.0 // Increased from 30s to 60s for Gemini 2.5 Flash Lite

            print("📡 [Gemini API] Sending request...")
            let networkStartTime = Date()

            let (data, response) = try await URLSession.shared.data(for: request)

            let networkDuration = Date().timeIntervalSince(networkStartTime)
            print("📥 [Gemini API] Response received - Network time: \(String(format: "%.2f", networkDuration))s, Data size: \(data.count) bytes")

            guard let httpResponse = response as? HTTPURLResponse else {
                throw GeminiError.invalidResponseWithMessage("Invalid HTTP response")
            }

            guard httpResponse.statusCode == 200 else {
                if httpResponse.statusCode == 403 {
                    throw GeminiError.apiKeyNotConfigured("Gemini API key is invalid or access denied (403)")
                } else if httpResponse.statusCode == 429 {
                    throw GeminiError.rateLimitExceeded("Rate limit exceeded. Please try again later.")
                } else {
                    throw GeminiError.apiError("HTTP \(httpResponse.statusCode)")
                }
            }

            print("🔍 [Gemini API] Parsing response...")
            let parseStartTime = Date()

            guard let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                  let candidates = json["candidates"] as? [[String: Any]],
                  let firstCandidate = candidates.first,
                  let content = firstCandidate["content"] as? [String: Any],
                  let parts = content["parts"] as? [[String: Any]],
                  let firstPart = parts.first,
                  let text = firstPart["text"] as? String else {
                print("❌ [Gemini API] Failed to parse response")
                throw GeminiError.parsingErrorWithMessage("Invalid response structure from Gemini API")
            }

            // Clean the text and remove code block markers
            let cleanedText = text.trimmingCharacters(in: .whitespacesAndNewlines)
                .replacingOccurrences(of: "```json", with: "")
                .replacingOccurrences(of: "```", with: "")
                .trimmingCharacters(in: .whitespacesAndNewlines)

            let parseDuration = Date().timeIntervalSince(parseStartTime)
            let totalDuration = Date().timeIntervalSince(startTime)
            print("✅ [Gemini API] Success - Parse: \(String(format: "%.2f", parseDuration))s, Total: \(String(format: "%.2f", totalDuration))s, Response: \(cleanedText.count) chars")

            return cleanedText

        } catch {
            let totalDuration = Date().timeIntervalSince(startTime)
            print("❌ [Gemini API] Error after \(String(format: "%.2f", totalDuration))s: \(error.localizedDescription)")
            if error is GeminiError {
                throw error
            }
            throw GeminiError.networkErrorWithMessage("Network request failed: \(error.localizedDescription)")
        }
    }


// MARK: - Error Types (Enhanced for Task 31)

enum GeminiError: Error, LocalizedError {
    case apiKeyNotConfigured(String)
    case invalidURL
    case invalidImage
    case invalidResponseWithMessage(String)
    case parsingErrorWithMessage(String)
    case networkErrorWithMessage(String)
    case invalidInput(String)
    case apiError(String)
    case rateLimitExceeded(String)

    // Legacy cases for backward compatibility
    case invalidResponse
    case parsingError
    case networkError

    var errorDescription: String? {
        switch self {
        case .apiKeyNotConfigured(let message):
            return message
        case .invalidURL:
            return "Invalid Gemini API URL"
        case .invalidImage:
            return "Invalid image data"
        case .invalidResponseWithMessage(let message):
            return "Invalid response from Gemini API: \(message)"
        case .parsingErrorWithMessage(let message):
            return "Failed to parse Gemini response: \(message)"
        case .networkErrorWithMessage(let message):
            return "Network error occurred: \(message)"
        case .invalidInput(let message):
            return "Invalid input: \(message)"
        case .apiError(let message):
            return "API error: \(message)"
        case .rateLimitExceeded(let message):
            return "Rate limit exceeded: \(message)"
        // Legacy cases
        case .invalidResponse:
            return "Invalid response from Gemini API"
        case .parsingError:
            return "Failed to parse Gemini response"
        case .networkError:
            return "Network error occurred"
        }
    }
    }


    // MARK: - Recipe Detail Generation (Phase 3)

    // MARK: - Recipe Detail Generation with Base Recipe (V12)

    /// Generate detailed recipe by expanding base recipe (NEW)
    func generateRecipeDetail(
        baseRecipe: Recipe,
        pantryContext: String? = nil,
        equipmentOwned: [String] = []
    ) async throws -> RecipeDetail {
        // Validate inputs
        guard !baseRecipe.recipeTitle.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            throw GeminiError.invalidInput("Recipe title cannot be empty")
        }

        // Build detail prompt with base recipe
        let prompt = buildRecipeDetailPrompt(
            baseRecipe: baseRecipe,
            pantryContext: pantryContext,
            equipmentOwned: equipmentOwned
        )

        // Call Gemini API with token limit for recipe detail
        let response = try await callGeminiAPI(prompt: prompt, maxTokens: 1024)

        // Parse response into RecipeDetail
        return try parseRecipeDetailResponse(response)
    }

    /// Build prompt that expands base recipe (NEW)
    private func buildRecipeDetailPrompt(
        baseRecipe: Recipe,
        pantryContext: String?,
        equipmentOwned: [String]
    ) -> String {
        var prompt = """
        Expand recipe: "\(baseRecipe.recipeTitle)". Servings: \(baseRecipe.servings). Time: \(baseRecipe.cookingTime). Difficulty: \(baseRecipe.difficulty.rawValue). Ingredients: \(baseRecipe.ingredients.joined(separator: ", ")).

        MUST MAINTAIN: servings=\(baseRecipe.servings), difficulty=\(baseRecipe.difficulty.rawValue), time≈\(baseRecipe.cookingTime) (±10%), all ingredients.

        Task: Add measurements to ingredients. Generate 6-12 specific steps with temps/times.
        """

        if let pantryContext = pantryContext, !pantryContext.isEmpty {
            prompt += " Pantry: \(pantryContext)."
        }

        if !equipmentOwned.isEmpty {
            prompt += " Equipment: \(equipmentOwned.joined(separator: ", "))."
        }

        prompt += """

        JSON format:
        {"title":"\(baseRecipe.recipeTitle)","description":"Brief 1-2 sentence description of the dish","servings":\(baseRecipe.servings),"totalTimeMinutes":<number>,"difficulty":"\(baseRecipe.difficulty.rawValue)","ingredients":["2 cups flour","1 tsp salt"],"steps":["Step 1","Step 2"],"nutrition":{"calories":320,"protein":"12g","carbs":"45g","fat":"8g"}}
        """

        return prompt
    }

    // MARK: - Recipe Detail Generation (Legacy - Title Only)

    // DEPRECATED: Use generateRecipeDetail(baseRecipe:) instead
    // This method is kept for backward compatibility only
    /// Generate detailed recipe information for a given recipe title
    func generateRecipeDetail(
        title: String,
        pantryContext: String? = nil,
        cuisines: [String] = [],
        equipmentOwned: [String] = []
    ) async throws -> RecipeDetail {
        // Validate inputs
        guard !title.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            throw GeminiError.invalidInput("Recipe title cannot be empty")
        }

        // Build detail prompt
        let prompt = buildRecipeDetailPrompt(
            title: title,
            pantryContext: pantryContext,
            cuisines: cuisines,
            equipmentOwned: equipmentOwned
        )

        // Call Gemini API with token limit for recipe detail (legacy method)
        let response = try await callGeminiAPI(prompt: prompt, maxTokens: 1024)

        // Parse response into RecipeDetail
        return try parseRecipeDetailResponse(response)
    }

    /// Build a structured prompt for recipe detail generation
    private func buildRecipeDetailPrompt(
        title: String,
        pantryContext: String?,
        cuisines: [String],
        equipmentOwned: [String]
    ) -> String {
        var prompt = """
        You are a professional chef and recipe developer. Generate a detailed recipe for: "\(title)"

        Requirements:
        1. Create a complete, actionable recipe with specific ingredients and step-by-step instructions
        2. Include precise measurements, cooking times, and temperatures where applicable
        3. Provide 6-12 detailed cooking steps that are clear and specific
        4. Avoid generic instructions like "cook as preferred" or "season to taste"
        5. Include realistic cooking and prep times

        """

        if let pantryContext = pantryContext, !pantryContext.isEmpty {
            prompt += """

            Available pantry context:
            \(pantryContext)

            """
        }

        if !cuisines.isEmpty {
            prompt += """

            Preferred cuisines: \(cuisines.joined(separator: ", "))

            """
        }

        if !equipmentOwned.isEmpty {
            prompt += """

            Available equipment: \(equipmentOwned.joined(separator: ", "))
            Tailor cooking methods to use available equipment. Avoid suggesting equipment not listed.

            """
        }

        prompt += """

        Return ONLY a JSON object with this exact format:
        {
          "title": "recipe title",
          "description": "Brief 1-2 sentence description of the dish",
          "servings": 4,
          "totalTimeMinutes": 45,
          "difficulty": "easy",
          "ingredients": [
            "2 cups all-purpose flour",
            "1 tsp salt",
            "3 tbsp olive oil"
          ],
          "steps": [
            "Preheat oven to 375°F (190°C).",
            "In a large bowl, whisk together flour and salt.",
            "Add olive oil and mix until combined.",
            "Continue with specific cooking instructions..."
          ],
          "nutrition": {
            "calories": 320,
            "protein": "12g",
            "carbs": "45g",
            "fat": "8g"
          }
        }

        Ensure:
        - description is a brief, appetizing 1-2 sentence summary
        - ingredients array has 5-15 items with specific measurements
        - steps array has 6-12 detailed, actionable instructions
        - difficulty is one of: "easy", "medium", "hard"
        - totalTimeMinutes includes prep + cooking time
        - nutrition values are realistic estimates
        """

        return prompt
    }

    /// Parse Gemini response into RecipeDetail
    private func parseRecipeDetailResponse(_ response: String) throws -> RecipeDetail {
        // Clean the response text (same logic as callGeminiAPI)
        let cleanedText = response.trimmingCharacters(in: .whitespacesAndNewlines)
            .replacingOccurrences(of: "```json", with: "")
            .replacingOccurrences(of: "```", with: "")
            .trimmingCharacters(in: .whitespacesAndNewlines)

        guard let jsonData = cleanedText.data(using: .utf8) else {
            throw GeminiError.invalidResponseWithMessage("Could not convert response to data")
        }

        do {
            let detail = try JSONDecoder().decode(RecipeDetail.self, from: jsonData)

            // Validate required fields
            guard !detail.title.isEmpty else {
                throw GeminiError.invalidResponseWithMessage("Recipe title is empty")
            }

            guard !detail.ingredients.isEmpty else {
                throw GeminiError.invalidResponseWithMessage("Recipe ingredients are empty")
            }

            guard !detail.steps.isEmpty else {
                throw GeminiError.invalidResponseWithMessage("Recipe steps are empty")
            }

            return detail
        } catch {
            throw GeminiError.parsingErrorWithMessage("Failed to parse recipe detail: \(error.localizedDescription)")
        }
    }

}

// MARK: - Gemini Category Mapper (Legacy)

enum GeminiCategoryMapper {
    static func map(categoryString: String, name: String) -> PantryCategory {
        let c = categoryString.lowercased().trimmingCharacters(in: .whitespacesAndNewlines)
        switch c {
        case "vegetables", "fruits", "produce":
            return .produce
        case "proteins", "protein":
            return .proteins
        case "grains", "pasta", "legumes":
            return .grainsPastaLegumes
        case "dairy":
            if IngredientNameNormalizer.isPlantBasedAlternative(name) { return .plantBasedAlternatives }
            return .dairy
        case "spices", "seasonings":
            return .spicesAndSeasonings
        case "oils", "condiments":
            return .oilsVinegarsAndCondiments
        case "plant-based", "vegan":
            return .plantBasedAlternatives
        case "bakery", "pastry":
            return .bakery
        case "canned", "broths", "packaged":
            return .cannedAndBroths
        case "snacks":
            return .snacks
        default:
            return .other
        }
    }
}
