import Foundation

/// P0 Telemetry event contract (no PII). Multi-value params encoded as JSON strings.
enum TelemetryEventBuilder {
    static func encodeJSON(_ dict: [String: Any]) -> String {
        guard let data = try? JSONSerialization.data(withJSONObject: dict, options: []) else { return "{}" }
        return String(data: data, encoding: .utf8) ?? "{}"
    }

    // Core P0 events
    static func generate_click(mode: String, pantryCount: Int) -> (name: String, params: [String: Any]) {
        ("generate_click", [
            "mode": mode,
            "pantry_count": pantryCount
        ])
    }

    static func generate_result(mode: String, resultCount: Int, durationMs: Int, requestJSON: String) -> (name: String, params: [String: Any]) {
        ("generate_result", [
            "mode": mode,
            "result_count": resultCount,
            "duration_ms": durationMs,
            "request": requestJSON
        ])
    }

    static func cancel_generate(mode: String, reason: String) -> (name: String, params: [String: Any]) {
        ("cancel_generate", [
            "mode": mode,
            "reason": reason
        ])
    }

    static func remote_config_fetch(source: String, success: Bool, durationMs: Int) -> (name: String, params: [String: Any]) {
        ("remote_config_fetch", [
            "source": source,
            "success": success,
            "duration_ms": durationMs
        ])
    }

    static func pantry_state_check(state: String, count: Int) -> (name: String, params: [String: Any]) {
        ("pantry_state_check", [
            "state": state,
            "count": count
        ])
    }

    static func structured_pantries_empty() -> (name: String, params: [String: Any]) {
        ("structured_pantries_empty", [:])
    }

    static func structured_slots_empty(mealTypes: [String], days: Int) -> (name: String, params: [String: Any]) {
        ("structured_slots_empty", [
            "meal_types": mealTypes.joined(separator: ","),
            "days": days
        ])
    }

    static func ui_performance(screen: String, avgFps: Int, firstPaintMs: Int) -> (name: String, params: [String: Any]) {
        ("ui_performance", [
            "screen": screen,
            "avg_fps": avgFps,
            "first_paint_ms": firstPaintMs
        ])
    }

    // P2: Feedback event (keep in base file to ensure target membership)
    static func user_feedback(like: Bool?, rating: Int?) -> (name: String, params: [String: Any]) {
        var params: [String: Any] = [:]
        if let like = like { params["like"] = like }
        if let rating = rating { params["rating"] = rating }
        return ("user_feedback", params)
    }
}
