# V11 Gemini 2.5 Flash Migration Documentation Hub

## Document Control
- Maintainer: iOS Platform – AI Integrations
- Reviewers: Feature Owners (Quick Recipes, Meal Plan, Vision), QA Automation
- Version: 2.0
- Last Updated: 2025-01-27
- Status: **CRITICAL - Production Fix Required**

## Purpose
This README consolidates the documentation and engineering standards for migrating from deprecated Gemini 1.5 models to Gemini 2.5 Flash, following Google's official migration guide. This is both a regression fix and a mandatory upgrade before the September 24, 2025 retirement deadline.

## Critical Migration Requirements (Google Official)
1. **Model Endpoint Migration**: Replace `models/gemini-1.5-flash-latest` with `models/gemini-2.5-flash` using the full endpoint format `https://generativelanguage.googleapis.com/v1beta/models/<modelID>:generateContent`.
2. **SDK Recommendation**: Google recommends migrating to Gen AI SDK, but custom HTTP calls are acceptable if they follow Gemini 2.x API specifications.
3. **Breaking Parameter Changes**: Remove `topK` parameter - not supported in Gemini 2.5+. Adjust `topP` if needed for similar behavior.
4. **Pricing Model Change**: Gemini 2.5+ uses token-based pricing instead of character-based. Update analytics and cost tracking accordingly.
5. **Content Filter Updates**: Review default content filter settings as they may have changed between versions.

## Technical Standards
1. **Gemini 2.5 Flash Primary**: All production traffic must target `models/gemini-2.5-flash`. Fallback to `models/gemini-2.5-flash-lite` only.
2. **Configuration First**: All model IDs, endpoints, and parameters must resolve from `GeminiConfig` (plist-backed). Zero hard-coded URLs in service classes.
3. **Single Entry Point**: `GeminiAPIService` remains the authoritative boundary. Other services submit semantic requests (prompt, modifiers, MIME types) rather than constructing URLs.
4. **Resilient Execution**: Primary model failures (404/410) must trigger automatic fallback with structured logging including model ID, status code, and response snippet.
5. **Token-Based Observability**: Log token usage metrics (prompt tokens, candidate tokens, total tokens) for 100% of requests. Redact API keys but include model identifiers.
6. **Concurrency Discipline**: Maintain `GeminiAPIService` as an `actor`. Fallback attempts run sequentially to avoid rate limit violations.
7. **Testing Rigor**: Unit tests must cover configuration loading, fallback sequencing, and parameter validation. Integration tests use `URLProtocol` mocks.

## Implementation Workflow (Immediate Action Required)
1. **Emergency Fix (Day 1 - Critical)**
   - **IMMEDIATE**: Update hardcoded URLs in `Services/GeminiAPIService.swift` and `Services/RecipeGenerationService.swift`
   - **IMMEDIATE**: Remove `topK` parameter from all generation configs
   - **IMMEDIATE**: Add basic `GeminiConfig` with plist loading
   - **IMMEDIATE**: Test basic functionality (Quick recipes, Meal Plan generation)

2. **Configuration Layer (Day 1-2)**
   - Define complete `GeminiConfig` struct with primary/fallback model support
   - Default to `models/gemini-2.5-flash` with `models/gemini-2.5-flash-lite` fallback
   - Add `GeminiPrimaryModel` and `GeminiFallbackModels` keys to `Application/api-keys.plist`
   - Update `Utilities/APIKeys.swift` to load model configuration

3. **Service Consolidation (Day 2-3)**
   - Eliminate duplicate URL construction in `RecipeGenerationService`
   - Implement unified `generateContent(request:context:)` method in `GeminiAPIService`
   - Add parameter validation to block deprecated Gemini 1.x options
   - Ensure all endpoints use the format: `https://generativelanguage.googleapis.com/v1beta/models/<modelID>:generateContent`

4. **Resilience & Monitoring (Day 3-4)**
   - Implement automatic fallback on 404/410 responses
   - Add structured logging with model ID, status code, and response snippets
   - Implement token usage tracking and analytics
   - Add user-friendly error messages with developer diagnostics in debug builds

5. **Testing & Validation (Day 4-5)**
   - Add unit tests for configuration loading and fallback logic
   - Create integration tests using `URLProtocol` mocks
   - Execute manual QA across all Gemini-powered features
   - Validate token-based analytics and cost tracking

## Testing Expectations
- **Critical Path Testing**: Verify Quick recipes, Meal Plan generation, recipe details, and image recognition work end-to-end
- **Unit Tests**: `GeminiAPIServiceTests` for configuration loading, URL construction, fallback sequencing, and parameter validation
- **Integration Tests**: `GeminiEndpointIntegrationTests` using `URLProtocol` mocks to simulate 404 responses and fallback success
- **Manual QA Checklist**:
  - Generate Quick recipe with production API key
  - Create structured Meal Plan with multiple meals
  - Test recipe detail enrichment
  - Verify image-to-text extraction
  - Force primary model failure to test fallback
- **Parameter Audit**: Static code analysis to ensure no `topK` parameters remain in any request
- **Endpoint Verification**: Network traces must show only `models/gemini-2.5-flash` or configured fallback URLs
- **Token Analytics**: Confirm token usage metrics appear in logs and analytics dashboards

## Collaboration & Reviews
- All PRs must reference the PRD section addressed (e.g., `FR-3.1`).
- Require sign-off from at least one QA reviewer before merge.
- Coordinate config changes with DevOps to avoid environment drift; include config diff in PR description.

## File Index
- `IMMEDIATE-FIX-GUIDE.md` – **START HERE** - Emergency fix instructions (30-60 minutes)
- `gemini-endpoint-regression-prd.md` – Complete product requirements and technical scope
- `task-breakdown.json` – Detailed task breakdown with priorities and timelines
- `code-review-and-migration-recommendations.md` – Code audit findings and recommendations

## IMMEDIATE ACTIONS REQUIRED

### 🚨 CRITICAL (Next 1 Hour)
1. **EMERGENCY FIX**: Update hardcoded URLs in `GeminiAPIService.swift` and `RecipeGenerationService.swift`
2. **EMERGENCY FIX**: Remove `topK` parameter from all generation configs
3. **TEST IMMEDIATELY**: Verify Quick recipes and Meal Plan generation work

### 📋 Next Steps (Days 1-5)
1. Implement complete `GeminiConfig` with fallback support
2. Add comprehensive error logging and token analytics
3. Create unit and integration tests for new functionality
4. Execute full QA validation across all AI features
5. **DECISION NEEDED**: Stay with custom HTTP or migrate to Gen AI SDK

### 📖 Reference Documents
- `IMMEDIATE-FIX-GUIDE.md` - Step-by-step emergency fix instructions
- `gemini-endpoint-regression-prd.md` - Complete requirements and scope
- `task-breakdown.json` - Detailed task breakdown and timeline
