#!/usr/bin/env swift

import Foundation

// Final validation test for Gemini 2.5 Flash migration
struct FinalValidationTest {
    private let geminiAPIKey = "AIzaSyAkx2xdXCq7hoWXfGtqHiEUWVvMp0u0QfM"
    private let visionAPIKey = "AIzaSyAkx2xdXCq7hoWXfGtqHiEUWVvMp0u0QfM"
    
    func runFinalValidation() async {
        print("🎯 Final Gemini 2.5 Flash Migration Validation")
        print("=" * 50)
        
        do {
            // Test 1: Basic Gemini 2.5 Flash connectivity
            print("\n✅ Test 1: Gemini 2.5 Flash Basic Test")
            try await testGeminiBasic()
            
            // Test 2: Recipe generation (core app functionality)
            print("\n🍳 Test 2: Recipe Generation Test")
            try await testRecipeGeneration()
            
            // Test 3: Vision API (image recognition)
            print("\n👁️ Test 3: Vision API Test")
            try await testVisionAPI()
            
            // Test 4: Parameter compliance (no topK)
            print("\n⚙️ Test 4: Parameter Compliance Test")
            try await testParameterCompliance()
            
            print("\n🎉 ALL TESTS PASSED!")
            print("✅ Gemini 2.5 Flash migration is successful")
            print("✅ All AI features should work correctly")
            print("✅ Ready for production deployment")
            
        } catch {
            print("\n❌ Final validation failed: \(error)")
            exit(1)
        }
    }
    
    private func testGeminiBasic() async throws {
        let url = URL(string: "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent?key=\(geminiAPIKey)")!
        
        let requestBody: [String: Any] = [
            "contents": [
                [
                    "parts": [
                        ["text": "Say 'Gemini 2.5 Flash is working!' and nothing else."]
                    ]
                ]
            ]
        ]
        
        let response = try await makeAPICall(url: url, requestBody: requestBody)
        
        guard response.contains("Gemini 2.5 Flash is working") else {
            throw ValidationError.invalidResponse("Basic test failed")
        }
        
        print("   ✓ Gemini 2.5 Flash API responding correctly")
        print("   ✓ Response: '\(response)'")
    }
    
    private func testRecipeGeneration() async throws {
        let url = URL(string: "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent?key=\(geminiAPIKey)")!
        
        let requestBody: [String: Any] = [
            "contents": [
                [
                    "parts": [
                        ["text": "Generate a simple recipe title using chicken and rice. Return only the recipe title, nothing else."]
                    ]
                ]
            ],
            "generationConfig": [
                "temperature": 0.7,
                "topP": 0.95,
                "maxOutputTokens": 50
                // Note: No topK parameter - Gemini 2.5+ doesn't support it
            ]
        ]
        
        let response = try await makeAPICall(url: url, requestBody: requestBody)
        
        guard !response.isEmpty && (response.lowercased().contains("chicken") || response.lowercased().contains("rice")) else {
            throw ValidationError.invalidResponse("Recipe generation failed")
        }
        
        print("   ✓ Recipe generation working")
        print("   ✓ Generated: '\(response)'")
    }
    
    private func testVisionAPI() async throws {
        let url = URL(string: "https://vision.googleapis.com/v1/images:annotate?key=\(visionAPIKey)")!
        
        // Simple 1x1 white pixel image
        let testImageBase64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
        
        let requestBody: [String: Any] = [
            "requests": [
                [
                    "image": [
                        "content": testImageBase64
                    ],
                    "features": [
                        ["type": "LABEL_DETECTION", "maxResults": 3]
                    ]
                ]
            ]
        ]
        
        let jsonData = try JSONSerialization.data(withJSONObject: requestBody)
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.httpBody = jsonData
        request.timeoutInterval = 30.0
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw ValidationError.apiError("Vision API request failed")
        }
        
        guard let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
              let _ = json["responses"] as? [[String: Any]] else {
            throw ValidationError.invalidResponse("Invalid Vision API response")
        }
        
        print("   ✓ Google Vision API working")
        print("   ✓ Image recognition functional")
    }
    
    private func testParameterCompliance() async throws {
        let url = URL(string: "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent?key=\(geminiAPIKey)")!
        
        let requestBody: [String: Any] = [
            "contents": [
                [
                    "parts": [
                        ["text": "Say 'Parameters OK' if you received this request."]
                    ]
                ]
            ],
            "generationConfig": [
                "temperature": 0.5,
                "topP": 0.9,
                "maxOutputTokens": 20
                // Explicitly NO topK parameter - testing compliance
            ]
        ]
        
        let response = try await makeAPICall(url: url, requestBody: requestBody)
        
        guard response.contains("Parameters OK") || response.contains("OK") else {
            throw ValidationError.invalidResponse("Parameter compliance test failed")
        }
        
        print("   ✓ No deprecated parameters used")
        print("   ✓ Gemini 2.5+ parameter compliance verified")
    }
    
    private func makeAPICall(url: URL, requestBody: [String: Any]) async throws -> String {
        let jsonData = try JSONSerialization.data(withJSONObject: requestBody)
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.httpBody = jsonData
        request.timeoutInterval = 30.0
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw ValidationError.apiError("API request failed")
        }
        
        guard let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
              let candidates = json["candidates"] as? [[String: Any]],
              let firstCandidate = candidates.first,
              let content = firstCandidate["content"] as? [String: Any],
              let parts = content["parts"] as? [[String: Any]],
              let firstPart = parts.first,
              let text = firstPart["text"] as? String else {
            throw ValidationError.invalidResponse("Invalid API response format")
        }
        
        return text.trimmingCharacters(in: .whitespacesAndNewlines)
    }
}

enum ValidationError: Error, LocalizedError {
    case invalidResponse(String)
    case apiError(String)
    
    var errorDescription: String? {
        switch self {
        case .invalidResponse(let message):
            return "Invalid response: \(message)"
        case .apiError(let message):
            return "API error: \(message)"
        }
    }
}

// String extension for repeat
extension String {
    static func * (string: String, count: Int) -> String {
        return String(repeating: string, count: count)
    }
}

// Run the final validation
Task {
    let validator = FinalValidationTest()
    await validator.runFinalValidation()
}

// Keep the script running
RunLoop.main.run()
