{"project": "V11 Gemini 2.5 Flash Migration", "description": "CRITICAL: Migrate from deprecated Gemini 1.5 to Gemini 2.5 Flash following Google's official migration guide to restore all AI features.", "reference": "https://cloud.google.com/vertex-ai/generative-ai/docs/migrate", "environment": {"language": "Swift", "platform": "iOS", "deployment_target": "17.0", "ui_framework": "SwiftUI", "concurrency": "Swift Concurrency", "build_system": "XcodeGen"}, "phases": [{"phase": "EMERGENCY FIX - Critical Production Issue", "window": "IMMEDIATE (Hours)", "tasks": [{"id": "EMERGENCY-1", "title": "Fix Hardcoded Model URLs", "description": "IMMEDIATE: Replace all hardcoded gemini-1.5-flash-latest URLs with publishers/google/models/gemini-2.5-flash", "priority": "P0-CRITICAL", "status": "completed", "prd_reference": "FR-0.1", "files_to_modify": ["Services/GeminiAPIService.swift", "Services/RecipeGenerationService.swift"], "acceptance_criteria": ["GeminiAPIService.swift line 6: Update baseURL to use publishers/google/models/gemini-2.5-flash", "RecipeGenerationService.swift line 132: Update URL construction to use publishers/google/models/gemini-2.5-flash", "All network requests target the new endpoint format", "Quick recipe generation works with production API key"]}, {"id": "EMERGENCY-2", "title": "Remove topK Parameter", "description": "IMMEDIATE: Remove topK parameter from all generation configs (not supported in Gemini 2.5+)", "priority": "P0-CRITICAL", "status": "completed", "prd_reference": "FR-0.2", "files_to_modify": ["Services/RecipeGenerationService.swift"], "acceptance_criteria": ["Remove topK: 40 from generationConfig in processRecipeText method", "Verify no other topK parameters exist in codebase", "API requests succeed without parameter errors", "Meal Plan generation works end-to-end"]}, {"id": "CFG-1", "title": "Basic GeminiConfig Implementation", "description": "Add basic configuration structure to support model switching and future enhancements", "priority": "P0", "prd_reference": "FR-1.1, FR-1.2", "files_to_modify": ["Application/api-keys.plist", "Utilities/APIKeys.swift", "Services/GeminiAPIService.swift"], "acceptance_criteria": ["Add GeminiPrimaryModel and GeminiFallbackModels keys to api-keys.plist", "Default primary: publishers/google/models/gemini-2.5-flash", "Default fallback: publishers/google/models/gemini-2.5-flash-lite", "APIKeys.swift loads model configuration from plist", "GeminiAPIService uses configured model instead of hardcoded URL"]}, {"id": "CFG-2", "title": "Refactor GeminiAPIService", "description": "Expose unified generateContent(request:context:) API sourcing endpoint paths from GeminiConfig.", "priority": "P0", "prd_reference": "FR-1.3, FR-2.1", "files_to_modify": ["Services/GeminiAPIService.swift"], "acceptance_criteria": ["Service constructs URL from config", "All public methods async and actor-isolated", "Unsupported Gemini 1.x parameters (for example topK) blocked or ignored with tests", "Endpoints append :generateContent to the configured model path"]}]}, {"phase": "Consumer Migration & Resiliency", "window": "Day 2", "tasks": [{"id": "MIG-1", "title": "Update RecipeGenerationService", "description": "Route structured meal plan generation through the new GeminiAPIService entry point.", "priority": "P0", "prd_reference": "FR-2.1, FR-2.2", "files_to_modify": ["Services/RecipeGenerationService.swift"], "acceptance_criteria": ["No hard-coded model IDs in service", "Structured flow compiles and uses new API", "Requests reference publishers/google/models/gemini-2.5-flash IDs with :generateContent"]}, {"id": "MIG-2", "title": "Migrate Remaining Call Sites", "description": "Update Quick recipes, recipe detail, and vision flows to the unified GeminiAPIService.", "priority": "P0", "prd_reference": "FR-2.2, FR-2.3", "files_to_modify": ["Features/RecipeGenerator/*", "Features/Recipes/*", "Features/1_ImageCapture/*", "Services/RecipeDetailService.swift"], "acceptance_criteria": ["All Gemini calls share the same service", "Static analysis reveals no residual model strings", "All payloads omit deprecated parameters such as topK"]}, {"id": "RES-1", "title": "Implement Fallback & Logging", "description": "Add fallback retry on 404/410 responses with structured telemetry and developer diagnostics.", "priority": "P0", "prd_reference": "FR-3.1, FR-3.2, FR-3.4", "files_to_modify": ["Services/GeminiAPIService.swift", "Utilities/Logging/GeminiLogger.swift"], "acceptance_criteria": ["Fallback triggered when primary returns 404/410", "Logs capture model ID, status code, response snippet", "Debug builds display diagnostic summary"]}, {"id": "RES-2", "title": "User Messaging Improvements", "description": "Map Gemini errors to localized, user-friendly messages while preserving developer detail in logs.", "priority": "P1", "prd_reference": "FR-3.3", "files_to_modify": ["Features/Common/ErrorPresenter.swift", "Base.lproj/Localizable.strings"], "acceptance_criteria": ["404/410 surfaces default service-unavailable copy", "Unknown errors fall back to generic message", "Localization validated for en base"]}, {"id": "RES-3", "title": "Token Usage Telemetry", "description": "Update logging and analytics to emit token-based counts and the active Gemini 2.x model IDs.", "priority": "P0", "prd_reference": "FR-0.4, Success Metrics", "files_to_modify": ["Utilities/Logging/GeminiLogger.swift", "Analytics/TelemetryEvents.swift"], "acceptance_criteria": ["Token usage captured for 100% of requests", "Dashboards show gemini-2.0-flash (or override) per event", "Unit tests cover token payload formatting"]}]}, {"phase": "Testing & Validation", "window": "Day 3", "tasks": [{"id": "VAL-1", "title": "Unit & Integration Tests", "description": "Add coverage for config loading, fallback sequencing, and error handling via URLProtocol doubles.", "priority": "P0", "prd_reference": "FR-4.1, FR-4.2", "files_to_modify": ["Tests/GeminiAPIServiceTests.swift", "Tests/GeminiEndpointIntegrationTests.swift"], "acceptance_criteria": ["Tests pass locally and in CI", "Fallback scenario validated via simulated 404"]}, {"id": "VAL-2", "title": "Manual QA Sweep", "description": "Execute manual regression checklist across Quick, structured, detail, and vision flows.", "priority": "P0", "prd_reference": "Success Metrics, Acceptance Criteria", "files_to_modify": [], "acceptance_criteria": ["All flows succeed with production key", "Fallback path validated by forcing primary failure", "Telemetry events observed in dashboard"]}, {"id": "VAL-3", "title": "Release Readiness", "description": "Compile release notes, ensure config promotions, and align on rollout/monitoring plan.", "priority": "P1", "prd_reference": "Implementation Guidance, Acceptance Criteria", "files_to_modify": ["Docs/release-notes.md", "Monitoring/dashboards.md"], "acceptance_criteria": ["Release notes drafted and approved", "Telemetry alerts configured", "On-call briefed on fallback behavior"]}]}], "critical_validation_scenarios": [{"name": "Emergency Fix Validation", "description": "Verify immediate fixes restore basic functionality", "steps": ["Generate Quick recipe with production API key", "Create structured Meal Plan", "Load recipe details page", "Process ingredient image"], "expected_outcome": "All AI features work without 404 errors"}, {"name": "Parameter Compliance Check", "description": "Ensure no deprecated Gemini 1.x parameters remain", "steps": ["Search codebase for 'topK' references", "Verify generation configs only use supported parameters", "Test API requests don't return parameter errors"], "expected_outcome": "Zero deprecated parameters in use"}, {"name": "Endpoint Verification", "description": "Confirm all requests use correct Gemini 2.0 endpoints", "steps": ["Monitor network traffic during AI operations", "Verify URLs contain 'publishers/google/models/gemini-2.5-flash'", "Check no requests target old 'models/gemini-1.5-flash-latest' endpoint"], "expected_outcome": "100% of requests use new endpoint format"}], "success_metrics": {"immediate_fix": {"gemini_404_rate": "0% after emergency fix", "feature_restoration": "100% of AI features functional", "time_to_fix": "<=60 minutes from start to working app"}, "full_implementation": {"fallback_success_rate": ">=95%", "logging_coverage": ">=99% of failures include model ID and status code", "token_telemetry_coverage": "100% of requests emit token usage metrics for Gemini 2.5+", "test_coverage": ">=90% for new configuration and fallback logic"}}, "implementation_notes": {"emergency_priority": "Fix hardcoded URLs and remove topK parameter first - everything else is secondary", "google_compliance": "Must complete migration before September 24, 2025 Gemini 1.5 retirement", "sdk_decision": "Defer Gen AI SDK migration to focus on immediate production fix", "rollout_approach": "Emergency fix first, then incremental improvements with proper testing"}}