#!/usr/bin/env swift

import Foundation

// Simple test to verify Gemini 2.5 Flash
struct SimpleTest {
    private let apiKey = "AIzaSyAkx2xdXCq7hoWXfGtqHiEUWVvMp0u0QfM"
    
    func test() async {
        print("🧪 Simple Gemini 2.5 Flash Test")
        
        do {
            let url = URL(string: "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent?key=\(apiKey)")!
            
            let requestBody: [String: Any] = [
                "contents": [
                    [
                        "parts": [
                            ["text": "Say 'Hello World' and nothing else."]
                        ]
                    ]
                ]
            ]
            
            let jsonData = try JSONSerialization.data(withJSONObject: requestBody)
            
            var request = URLRequest(url: url)
            request.httpMethod = "POST"
            request.setValue("application/json", forHTTPHeaderField: "Content-Type")
            request.httpBody = jsonData
            request.timeoutInterval = 30.0
            
            let (data, response) = try await URLSession.shared.data(for: request)
            
            if let httpResponse = response as? HTTPURLResponse {
                print("Status Code: \(httpResponse.statusCode)")
            }
            
            let responseString = String(data: data, encoding: .utf8) ?? "Unable to decode response"
            print("Raw Response:")
            print(responseString)
            
            if let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any] {
                if let candidates = json["candidates"] as? [[String: Any]],
                   let firstCandidate = candidates.first,
                   let content = firstCandidate["content"] as? [String: Any],
                   let parts = content["parts"] as? [[String: Any]],
                   let firstPart = parts.first,
                   let text = firstPart["text"] as? String {
                    print("\n✅ SUCCESS!")
                    print("Extracted text: '\(text)'")
                    print("🎉 Gemini 2.5 Flash is working!")
                } else {
                    print("\n❌ Failed to parse response")
                }
            } else {
                print("\n❌ Invalid JSON response")
            }
            
        } catch {
            print("❌ Error: \(error)")
        }
    }
}

// Run test
Task {
    let test = SimpleTest()
    await test.test()
}

// Keep running
RunLoop.main.run()
