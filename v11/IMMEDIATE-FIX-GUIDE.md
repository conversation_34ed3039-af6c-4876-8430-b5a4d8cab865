# 🚨 IMMEDIATE FIX GUIDE - Gemini 2.5 Flash Migration

## CRITICAL: Production Outage - All AI Features Down

**Status**: All Gemini-powered features failing with HTTP 404
**Root Cause**: Using deprecated `gemini-1.5-flash-latest` endpoint
**Fix Required**: Migrate to `publishers/google/models/gemini-2.5-flash`
**Time to Fix**: 30-60 minutes

## Step 1: Fix Hardcoded URLs (IMMEDIATE - 10 minutes)

### File 1: `Services/GeminiAPIService.swift`
**Line 6** - Change:
```swift
// OLD (BROKEN)
private let baseURL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent"

// NEW (WORKING)
private let baseURL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent"
```

### File 2: `Services/RecipeGenerationService.swift`
**Line 132** - Change:
```swift
// OLD (BROKEN)
let url = URL(string: "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=\(APIKeys.geminiAPIKey)")

// NEW (WORKING)
let url = URL(string: "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent?key=\(APIKeys.geminiAPIKey)")
```

## Step 2: Remove Unsupported Parameters (IMMEDIATE - 5 minutes)

### File: `Services/RecipeGenerationService.swift`
**Lines 122-128** - Remove `topK` parameter:
```swift
// OLD (CAUSES ERRORS)
"generationConfig": [
    "temperature": 0.7,
    "topK": 40,  // ❌ DELETE THIS LINE
    "topP": 0.95,
    "maxOutputTokens": 2048,
    "response_mime_type": "application/json"
]

// NEW (WORKING)
"generationConfig": [
    "temperature": 0.7,
    // topK removed - not supported in Gemini 2.5+
    "topP": 0.95,
    "maxOutputTokens": 2048,
    "response_mime_type": "application/json"
]
```

## Step 3: Test Immediately (5 minutes)

1. **Build and run the app**
2. **Test Quick Recipe Generation**:
   - Go to Quick tab
   - Try generating a recipe
   - Should work without 404 errors

3. **Test Meal Plan Generation**:
   - Go to Plans tab  
   - Generate a new meal plan
   - Should complete successfully

4. **Test Recipe Details**:
   - Tap on any recipe
   - Recipe details should load

5. **Test Image Recognition**:
   - Take a photo of ingredients
   - Should process and return text

## Step 4: Add Basic Configuration (Optional - 15 minutes)

### File: `Application/api-keys.plist`
Add these keys:
```xml
<key>GeminiPrimaryModel</key>
<string>gemini-2.5-flash</string>
<key>GeminiFallbackModels</key>
<array>
    <string>gemini-2.5-flash-lite</string>
</array>
```

### File: `Utilities/APIKeys.swift`
Add configuration loading:
```swift
static let geminiPrimaryModel: String = {
    if let path = Bundle.main.path(forResource: "api-keys", ofType: "plist"),
       let plist = NSDictionary(contentsOfFile: path),
       let model = plist["GeminiPrimaryModel"] as? String {
        return model
    }
    return "gemini-2.5-flash"
}()

static let geminiFallbackModels: [String] = {
    if let path = Bundle.main.path(forResource: "api-keys", ofType: "plist"),
       let plist = NSDictionary(contentsOfFile: path),
       let models = plist["GeminiFallbackModels"] as? [String] {
        return models
    }
    return ["gemini-2.5-flash-lite"]
}()
```

## Verification Checklist

- [ ] **GeminiAPIService.swift**: URL updated to use `gemini-2.5-flash`
- [ ] **RecipeGenerationService.swift**: URL updated to use `gemini-2.5-flash`
- [ ] **RecipeGenerationService.swift**: `topK` parameter removed from generationConfig
- [ ] **Quick Recipes**: Generate a recipe successfully
- [ ] **Meal Plan**: Create a meal plan successfully  
- [ ] **Recipe Details**: Load recipe details successfully
- [ ] **Image Recognition**: Process ingredient image successfully
- [ ] **No 404 Errors**: All API calls return 200 or valid responses

## What This Fixes

✅ **Quick Recipe Generation** - No more "Failed to generate recipe" errors  
✅ **Meal Plan Creation** - Plans will generate and display properly  
✅ **Recipe Details** - Recipe enrichment will work  
✅ **Image Processing** - Ingredient recognition will function  
✅ **All AI Features** - Complete restoration of Gemini-powered functionality  

## Next Steps (After Immediate Fix)

1. **Implement proper fallback mechanism** (Day 2-3)
2. **Add comprehensive error logging** (Day 3-4)  
3. **Migrate to token-based analytics** (Day 4-5)
4. **Add unit and integration tests** (Day 5)

## Emergency Contacts

If this fix doesn't work:
1. Check API key validity in `Application/api-keys.plist`
2. Verify network connectivity
3. Check Xcode console for detailed error messages
4. Ensure you're using the exact URLs specified above

**This fix should restore all AI functionality within 30-60 minutes of implementation.**
