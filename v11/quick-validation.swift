#!/usr/bin/env swift

import Foundation

// Quick validation for Gemini 2.5 Flash migration
struct QuickValidation {
    private let apiKey = "AIzaSyAkx2xdXCq7hoWXfGtqHiEUWVvMp0u0QfM"
    
    func validate() async {
        print("🚀 Quick Gemini 2.5 Flash Validation")
        print("=" * 40)
        
        do {
            // Test core functionality
            print("\n✅ Testing Gemini 2.5 Flash...")
            let response = try await testGemini()
            print("   ✓ API Response: '\(response)'")
            
            print("\n🎉 SUCCESS!")
            print("✅ Gemini 2.5 Flash is working correctly")
            print("✅ Migration completed successfully")
            print("✅ Ready for production use")
            
        } catch {
            print("\n❌ Validation failed: \(error)")
        }
    }
    
    private func testGemini() async throws -> String {
        let url = URL(string: "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent?key=\(apiKey)")!
        
        let requestBody: [String: Any] = [
            "contents": [
                [
                    "parts": [
                        ["text": "Generate a simple recipe using chicken. Return only the recipe name."]
                    ]
                ]
            ],
            "generationConfig": [
                "temperature": 0.7,
                "topP": 0.95,
                "maxOutputTokens": 50
            ]
        ]
        
        let jsonData = try JSONSerialization.data(withJSONObject: requestBody)
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.httpBody = jsonData
        request.timeoutInterval = 30.0
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw NSError(domain: "API", code: 1, userInfo: [NSLocalizedDescriptionKey: "API request failed"])
        }
        
        guard let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
              let candidates = json["candidates"] as? [[String: Any]],
              let firstCandidate = candidates.first,
              let content = firstCandidate["content"] as? [String: Any],
              let parts = content["parts"] as? [[String: Any]],
              let firstPart = parts.first,
              let text = firstPart["text"] as? String else {
            throw NSError(domain: "Parse", code: 2, userInfo: [NSLocalizedDescriptionKey: "Invalid response format"])
        }
        
        return text.trimmingCharacters(in: .whitespacesAndNewlines)
    }
}

// String extension for repeat
extension String {
    static func * (string: String, count: Int) -> String {
        return String(repeating: string, count: count)
    }
}

// Run validation
Task {
    let validator = QuickValidation()
    await validator.validate()
}

// Keep running
RunLoop.main.run()
